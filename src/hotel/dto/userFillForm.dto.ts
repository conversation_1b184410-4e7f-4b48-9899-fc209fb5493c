import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsNotEmpty, IsString, IsBoolean, IsOptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength } from 'class-validator'

export class UserFillFormDto {
  @ApiProperty({
    description: 'The session id from previous search',
    example: '02JV3Q93R0'
  })
  @IsNotEmpty()
  @IsString()
  sessionId: string

  @ApiProperty({
    description: 'The sequence number from previous search',
    example: '5'
  })
  @IsNotEmpty()
  @IsString()
  sequenceNumber: string

  @ApiProperty({
    description: 'The security token from previous search',
    example: '1YVE4IWPFJ8NQ3MECH1J0L8KH0'
  })
  @IsNotEmpty()
  @IsString()
  securityToken: string
  
  @ApiProperty({
    description: 'The surname (last name) of the guest',
    example: '<PERSON>uy<PERSON>'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  surName: string

  @ApiProperty({
    description: 'The first name of the guest',
    example: 'Van <PERSON>'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  firstName: string

  @ApiProperty({
    description: 'The email address of the guest',
    example: '<EMAIL>'
  })
  @IsNotEmpty()
  @IsEmail()
  email: string

  @ApiProperty({
    description: 'The phone number of the guest (with country code)',
    example: '+84912345678'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  @MaxLength(20)
  phoneNumber: string

  @ApiProperty({
    description: 'Special requests for the booking (optional)',
    example: 'HIGH FLOOR REQUESTED',
    required: false
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  specialRequest?: string

  @ApiProperty({
    description: 'Smoking preference (true for smoking, false for non-smoking)',
    example: false
  })
  @IsNotEmpty()
  @IsBoolean()
  smokingCondition: boolean
}
