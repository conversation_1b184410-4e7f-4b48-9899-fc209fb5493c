---
applyTo: '**'
---
Coding standards, domain knowledge, and preferences that AI should follow.
---
applyTo: '**'
---
Coding standards, domain knowledge, and preferences that AI should follow.

## 🗣️ **General Interaction Guidelines with Copilot**
* When providing explanations, engaging in discussions, or generating descriptive text, please use **Vietnamese**.
* However, all **code comments must be written in English.**

## 🎯 **Project Goal & Technology Overview**

This project is a Python-based multi-agent system for automated research report generation. It may interact with or be complemented by a Next.js application for frontend and certain backend-for-frontend (BFF) functionalities.

**Core Python Backend Technologies:**

* **Python 3.10+**
* **FastAPI:** For exposing management and report generation APIs.
* **Langchain & LangGraph (or similar custom LLM Agent Framework):** For agent orchestration and multi-agent system (MAS) implementation. The system involves defining agents, their tools, a state graph for managing interactions, and supervisor logic for coordination.
* **Pydantic:** For data validation, settings management, and defining schemas for agent states/messages.
* **SQLAlchemy (async):** For interacting with a PostgreSQL database (storing research data, user preferences, generated reports, agent states).
* **Celery with <PERSON>is:** For background task processing (e.g., long-running research tasks, report assembly).
* **Pytest:** For unit and integration testing.
* **Loguru:** For structured logging.

**Core JavaScript/Next.js Frontend/BFF Technologies (If Applicable):**
* **Next.js (App Router preferred):** For building the user interface and potentially handling API routes/Route Handlers as a Backend-for-Frontend.
* **TypeScript:** Preferred for all JavaScript/Next.js development.
* **React:** As the UI library within Next.js.
* **Zod:** For data validation in Next.js backend handlers.
* **(Testing Libraries e.g., Jest, Vitest, Playwright):** For testing Next.js components and API handlers.

## 📜 **Global Coding Standards (Apply to both Python & JS/TS)**

* **Code Style:**
    * Python: Follow **PEP8 guidelines** strictly.
    * JS/TS: Use a consistent formatter like **Prettier** and a linter like **ESLint** with a standard configuration (e.g., Airbnb, StandardJS adapted for TypeScript).
* **Type Hints/Typing:**
    * Python: All code must include **Python type hints** for function signatures, variables, and class attributes.
    * JS/TS: Use **TypeScript** for all new code. Define types/interfaces for props, state, function signatures, API payloads, etc.
* **Docstrings/JSDoc:**
    * Python: **Comprehensive docstrings (Google style)** are required for all modules, classes, public functions, and methods.
    * JS/TS: Use **JSDoc comments** for all exported functions, classes, methods, and complex type definitions. Explain purpose, parameters, return values.
* **Readability & Maintainability:** Prioritize **readable, maintainable, and self-documenting code.** Avoid overly complex expressions; break them down.
* **Comments (In English):** Comments should explain **why** something is done, not **what** is being done if the code is already clear.

## ⚙️ **Professional Development Processes**

### **1. Task Breakdown and Planning**

Before generating code for a new agent, complex API endpoint (Python or Next.js), significant feature, or UI component:

1.  **Outline the main functional steps** or logical sub-components.
2.  Consider **data flow, input/output requirements, state management.**
3.  Identify potential **edge cases and error handling strategies.**
4.  List any **external dependencies, APIs, or tools.**
5.  **For LLM Agents (Python):**
    * Define the agent's role, capabilities, and persona. Outline key information/context needed.
6.  **For Next.js UI/BFF:**
    * Sketch component structure, props, and state.
    * Define API interactions if it's a BFF endpoint.
7.  Present this outline or plan **before generating the full code.**

### **2. Plan Adherence & Architecture**

* Ensure alignment with the overall **system architecture** defined in `docs/SYSTEM_ARCHITECTURE.md` (covering both Python backend and any Next.js frontend/BFF).
* If addressing an issue, refer to its description for goals and acceptance criteria.
* Indicate which part of the plan each code block addresses for complex tasks.

### **3. Function Generalization and Reusability**

* When creating helper functions (Python or JS/TS):
    * Evaluate if core logic can be **generalized** for broader application.
    * Design with **clear, well-typed input parameters** and a focused output, minimizing side effects.
    * **Parameterize effectively;** avoid hardcoding.
    * Place reusable, domain-agnostic functions in appropriate utility directories (e.g., `src/utils/` in Python, `src/lib/utils/` in Next.js).

---

## 🐍 **Python Code Refactoring Guidelines**

This section provides instructions for Copilot when requested to "optimize file structure" or "refactor the project" related to Python code.

### **📄 Refactoring Large Files (Single File Refactoring)**

When a Python file becomes too large:

1.  **Identify Logical Groupings:** Separate classes into own files; group utility functions.
2.  **Single Responsibility Principle (SRP):** Each module should have one primary responsibility.
3.  **Create Subdirectories (Packages) if Necessary:** Ensure `__init__.py`.
4.  **Manage Imports:** Update imports post-refactor; use relative imports within packages.

### **🏗️ Project Structure Refactoring (Python)**

1.  **Adhere to Standard Project Structure:** Refer to the Python project structure defined below.
2.  **Modularity and Loose Coupling:** Design for modularity; use Dependency Injection.
3.  **Clarity and Consistency:** Use clear, descriptive names for files/directories.

### ✨ **Example Python Refactoring Prompts:**
* "Copilot, analyze `big_file.py` and suggest how to break it into smaller modules, following our project structure."
* "Copilot, this file violates SRP. Help refactor it into correct modules under `src/services` or `src/agents`."

---

## 📁 **Project Directory Structures**

### **Python Backend Project Structure**

* **`src/`**
    * **`api/`**: FastAPI application.
        * `main.py`, `routers/`, `dependencies.py`
    * **`agents/`**: LLM agent definitions, orchestration.
        * `graph.py`, `supervisor.py`, `*_agent.py`, `tools/`, `prompts/`
    * **`core/`**: Core business logic, services.
        * `config.py`, `services/`
    * **`db/`**: Database related code.
        * `session.py`, `models.py`, `crud.py`
    * **`schemas/`**: Pydantic schemas.
        * `report_schemas.py`, `user_schemas.py`, `agent_schemas.py`
    * **`utils/`**: Common utility functions.
    * **`workers/`**: Celery task definitions.
* **`tests/`**: Pytest tests (mirroring `src/` structure).
* **`docs/`**: Project documentation.
    * `SYSTEM_ARCHITECTURE.md`, `API_DESIGN_GUIDELINES.md`, `AGENT_DESIGN_PRINCIPLES.md`

### **Next.js (Frontend/BFF) Project Structure (Example)**
*(Assuming Next.js project is in a separate repository or a distinct subdirectory like `frontend/` if in a monorepo)*

* **`frontend/`** (or project root if standalone Next.js)
    * **`src/`**
        * **`app/`** (App Router)
            * `layout.tsx`, `page.tsx`
            * **`api/`**: Route Handlers for BFF.
                * `[segment]/[...segments]/route.ts` (e.g., `app/api/bff/reports/route.ts`)
            * `(features)/` : Feature-based routing for UI components (e.g., `(reports)/_components/`).
            * `_components/` : Shared UI components.
            * `_lib/` : Client-side and server-side utility functions, hooks.
                * `client/`, `server/`
            * `_services/` : Client-side data fetching services or services for Route Handlers.
            * `_hooks/` : Custom React hooks.
            * `_contexts/` : React context definitions.
        * **`pages/`** (Pages Router - for existing parts or if App Router not fully adopted)
            * **`api/`**: API Routes.
                * `[segment]/[...segments].ts`
        * **`components/`**: Global/shared React components (if not using feature-based `_components` in `app/`).
        * **`lib/`**: Utility functions, constants.
        * **`styles/`**: Global styles, Tailwind configuration.
        * **`public/`**: Static assets.
    * **`tests/`**: Jest/Vitest/Playwright tests.
    * `next.config.js`, `tsconfig.json`, `tailwind.config.js`

---

## 🛠️ **Technology-Specific Instructions**

### **Python & FastAPI (Backend)**

* Endpoints: `async def`. Use Pydantic models from `src/schemas/`.
* Dependency Injection for DB sessions, auth.
* Refer to `docs/API_DESIGN_GUIDELINES.md`.
* Custom exception handlers for consistent error responses.

### **LLM Agents & MAS (Python Backend)**

1.  **Agent Roles & Prompting:** Clearly define agent persona, capabilities. Use `src/agents/prompts/` for complex prompts.
2.  **Tools (`src/agents/tools/`):** Well-documented Python functions. Grant necessary tools only. Docstrings should be LLM-understandable.
3.  **State Management (Orchestration Graph):** Define shared state (e.g., `ResearchState`) using Pydantic in `src/schemas/agent_schemas.py`. Managed by `src/agents/graph.py`.
4.  **Orchestration & Supervisor Logic:** Supervisor (`src/agents/supervisor.py`) breaks down tasks, delegates, manages workflow via graph.
5.  **Communication:** Primarily via shared graph state. Define message schemas if direct messaging is used.
6.  **Error Handling:** Robust error handling in agents/tools; report failures to supervisor/state.

### **SQLAlchemy & Database (Python Backend)**

* Use asynchronous SQLAlchemy. Manage sessions via FastAPI dependencies.
* ORM models in `src/db/models.py`. CRUD operations in `src/db/crud.py`.

### **JavaScript & Next.js Backend (API Routes / Route Handlers)**

#### **1. General Principles & Code Style (JS/TS)**

* **Language:** Prefer **TypeScript**.
* **ECMAScript Version:** Use modern ECMAScript features (ES2020+).
* **Asynchronous Operations:** `async/await` for all async operations. Handle Promises correctly.
* **Error Handling:** Robust `try...catch`. Consistent error response formats (e.g., `{ success: false, error: { message: '...', code: '...' } }`).
* **Modularity:** Break down logic into small, reusable functions/modules (`src/_lib/server/`, `src/_services/backend/`).
* **Logging (English Comments):** Structured logging for API requests, errors.
* **Environment Variables:** Use Next.js built-in support (`.env`, `NEXT_PUBLIC_`).

#### **2. Next.js Backend Specifics**

* **App Router (Preferred):** Use **Route Handlers** (`src/app/api/.../route.ts`).
* **Pages Router (Legacy):** API Routes (`src/pages/api/...ts`).
* **Request Handling:**
    * Handle HTTP methods correctly.
    * Validate request bodies, query/path params using **Zod**.
        ```typescript
        // Example Zod validation in a Route Handler
        // import { z } from 'zod';
        // const mySchema = z.object({ /* ... */ });
        // const result = mySchema.safeParse(await request.json());
        // if (!result.success) { /* return error */ }
        // const { data } = result; // Use validated data
        ```
* **Response Handling:** Appropriate HTTP status codes. Consistent JSON structures.
* **Typed API Endpoints:** Use TypeScript for request/response types.
* **Middleware (`middleware.ts`):** For auth, logging, redirects. Keep lean.
* **Server Actions (App Router):** For data mutations from Server Components/forms. Ensure error handling and revalidation (`revalidatePath`, `revalidateTag`).
* **Interacting with Python Backend (BFF Pattern):**
    * Use `fetch` API or `axios` from Next.js server-side to Python API.
    * Securely handle API keys/auth tokens for backend-to-backend communication.
* **Database Interaction (If Next.js backend needs direct DB access):**
    * Use Prisma, Kysely, or `node-postgres`. Manage connections efficiently.
    * Place DB logic in service files. Secure credential management.

#### **3. Security (JS/TS Backend)**

* **Input Validation (Zod):** Crucial for all incoming data.
* **Authentication & Authorization:** Protect endpoints (e.g., NextAuth.js, JWTs).
* **CSRF Protection:** Ensure protection for cookie-based auth & state-modifying forms. Server Actions have built-in CSRF.
* **Rate Limiting:** Implement on API endpoints.
* **Security Headers:** Set appropriately (`Content-Security-Policy`, etc.) via `next.config.js` or middleware.
* **Dependency Management:** Regularly update `npm`/`yarn` packages.

---

## 🧪 **Testing**

### **Python (Pytest)**

* Unit tests for services, utils, agent logic/tools.
* Integration tests for API endpoints (`httpx.AsyncClient`) and agent orchestration flows.
* Mock external dependencies. Aim for high test coverage.

### **JavaScript/Next.js (e.g., Jest, Vitest, Playwright)**

* **Unit Tests:** For individual functions, utilities, React components (if applicable to BFF-related components).
* **API Route/Handler Tests:**
    * Mock `Request` and `Response` objects or use libraries like `next-test-api-route-handler` (for Pages Router) or similar patterns for App Router.
    * Test HTTP methods, inputs, success/error conditions.
* **Integration Tests:** Test interactions between Route Handlers and any services/databases they use.
* **End-to-End Tests (Playwright/Cypress):** For critical user flows involving both frontend and BFF layers.
* Mock external dependencies (e.g., Python backend calls).

---

## 🛡️ **Global Security Guidelines (Apply to all code)**

* **Sanitize all inputs** from external sources to prevent injection attacks (SQLi, NoSQLi, XSS, Prompt Injection).
* **Use parameterized queries or ORM capabilities** for database interactions.
* Implement proper **authentication and authorization** for all APIs.
* Store sensitive information (API keys, DB credentials) securely using **environment variables** and configuration management (e.g., Pydantic's `BaseSettings`, Next.js `env` variables). **Never hardcode them.**
* Be mindful of data passed to and received from LLMs, especially sensitive information.
* **Regularly update all dependencies** (Python and JS) to patch known vulnerabilities.