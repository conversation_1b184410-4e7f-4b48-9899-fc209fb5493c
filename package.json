{"name": "travel-api", "version": "0.0.1", "description": "", "author": "NFTPassport", "private": true, "license": "MIT", "type": "commonjs", "scripts": {"build": "prisma generate && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "DEBUG=eslint:cli-engine npx eslint 'src/**/*.ts' 'src/*.ts'", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.0.11", "@nestjs/core": "^11.0.11", "@nestjs/platform-express": "^11.0.11", "@prisma/client": "^5.21.1", "crypto-js": "^4.2.0", "prisma": "^5.12.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0", "xml-js": "^1.6.11", "xml2js": "^0.6.2"}, "devDependencies": {"@fastify/static": "^8.1.1", "@nestjs/": "nestjs/config", "@nestjs/bull": "^10.1.1", "@nestjs/bullmq": "^10.2.3", "@nestjs/cache-manager": "^2.3.0", "@nestjs/cli": "^11.0.5", "@nestjs/config": "^4.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-fastify": "^11.0.11", "@nestjs/schematics": "^10.0.0", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.3.1", "@nestjs/testing": "^10.0.0", "@paypal/paypal-server-sdk": "^1.0.0", "@types/bcrypt": "^5.0.2", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/node-cron": "^3.0.11", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "axios": "^1.7.2", "bcrypt": "^5.1.1", "bullmq": "^5.31.1", "cache-manager": "^6.3.2", "class-validator": "^0.14.1", "csv-parse": "^5.6.0", "dotenv": "^16.4.5", "elastic-apm-node": "^4.11.1", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "ethers": "^6.12.0", "googleapis": "^148.0.0", "handlebars": "^4.7.8", "jest": "^29.5.0", "moment-timezone": "^0.5.46", "nestjs": "^0.0.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "stripe": "^18.0.0", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "uuid": "^11.0.3", "xmlbuilder2": "^3.1.1"}, "resolutions": {"string-width": "^4.2.0"}}