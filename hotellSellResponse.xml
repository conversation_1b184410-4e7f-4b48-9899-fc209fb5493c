<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:awsse="http://xml.amadeus.com/2010/06/Session_v3" xmlns:wsa="http://www.w3.org/2005/08/addressing">
    <soap:Header>
        <wsa:To>http://www.w3.org/2005/08/addressing/anonymous</wsa:To>
        <wsa:From>
            <wsa:Address>https://noded5.test.webservices.amadeus.com/1ASIWVIELT4</wsa:Address>
        </wsa:From>
        <wsa:Action>http://webservices.amadeus.com/HBKRCQ_24_2_1A</wsa:Action>
        <wsa:MessageID>urn:uuid:09bb1c95-76d2-5714-c92d-bbf9df492fa0</wsa:MessageID>
        <wsa:RelatesTo RelationshipType="http://www.w3.org/2005/08/addressing/reply">urn:uuid:be94c9c9-5ca4-4c8e-ae6e-6f594dc3dbe3</wsa:RelatesTo>
        <awsse:Session TransactionStatusCode="InSeries">
            <awsse:SessionId>001O1GF3T3</awsse:SessionId>
            <awsse:SequenceNumber>4</awsse:SequenceNumber>
            <awsse:SecurityToken>I78OI33UJ3Q02OY30HEUVV98C</awsse:SecurityToken>
        </awsse:Session>
    </soap:Header>
    <soap:Body>
        <Hotel_SellReply xmlns="http://xml.amadeus.com/HBKRCR_24_2_1A">
            <bookingTypeIndicator>
                <numberOfRooms>
                    <quantity>1</quantity>
                    <statusCode>HK</statusCode>
                </numberOfRooms>
            </bookingTypeIndicator>
            <roomStayData>
                <markerRoomStayData></markerRoomStayData>
                <pnrInfo>
                    <tattooReference>
                        <referenceDetails>
                            <type>S</type>
                            <value>1</value>
                        </referenceDetails>
                    </tattooReference>
                </pnrInfo>
                <globalBookingInfo>
                    <hotelPropertyInfo>
                        <hotelReference>
                            <chainCode>RT</chainCode>
                            <cityCode>MAD</cityCode>
                            <hotelCode>IIS</hotelCode>
                        </hotelReference>
                        <hotelName>IBIS MADRID AEROPUERTO</hotelName>
                    </hotelPropertyInfo>
                    <forceSellIndicator>
                        <statusDetails>
                            <indicator>FS</indicator>
                            <action>2</action>
                        </statusDetails>
                    </forceSellIndicator>
                    <individualCompanyId>
                        <companyName>ACCOR HOTELS</companyName>
                    </individualCompanyId>
                    <bookingInfo>
                        <reservation>
                            <companyId>RT</companyId>
                            <controlNumber>3753ZF3500</controlNumber>
                            <controlType>2</controlType>
                        </reservation>
                    </bookingInfo>
                    <bookingSource>
                        <originIdentification>
                            <originatorId>00886082</originatorId>
                        </originIdentification>
                    </bookingSource>
                    <globalPriceInformation>
                        <globalPrice>
                            <tariffInfo>
                                <amount>43.33</amount>
                                <currency>EUR</currency>
                                <dailyTotalIndicator>3</dailyTotalIndicator>
                                <totalAmount>72.00</totalAmount>
                            </tariffInfo>
                        </globalPrice>
                    </globalPriceInformation>
                    <representativeParties>
                        <occupantList>
                            <passengerReference>
                                <type>BHO</type>
                                <value>2</value>
                            </passengerReference>
                        </occupantList>
                        <guestContactInfo>
                            <phoneOrEmailType>EML</phoneOrEmailType>
                            <emailAddress><EMAIL></emailAddress>
                        </guestContactInfo>
                        <occupantPreferences>
                            <occupantPreferences>
                                <occupantLanguage>EN</occupantLanguage>
                            </occupantPreferences>
                        </occupantPreferences>
                    </representativeParties>
                </globalBookingInfo>
                <roomListInfo>
                    <roomStayIndex>
                        <sequenceDetails>
                            <number>1</number>
                        </sequenceDetails>
                    </roomStayIndex>
                    <markLinesAndRateDesc>
                        <freeTextQualification>
                            <textSubjectQualifier>2</textSubjectQualifier>
                            <informationTypeId>ML</informationTypeId>
                        </freeTextQualification>
                        <freeText>We have confirmed 1 C1DRA3 for 2 Persons at 52.00 EUR //\n t</freeText>
                    </markLinesAndRateDesc>
                    <commissionAndMarkup>
                        <commissionInfo>
                            <commissionDetails>
                                <type>UNK</type>
                            </commissionDetails>
                        </commissionInfo>
                    </commissionAndMarkup>
                    <requestableInformation>
                        <requestedDates>
                            <businessSemantic>CHK</businessSemantic>
                            <timeMode>CHK</timeMode>
                            <beginDateTime>
                                <year>2025</year>
                                <month>6</month>
                                <day>4</day>
                            </beginDateTime>
                            <endDateTime>
                                <year>2025</year>
                                <month>6</month>
                                <day>5</day>
                            </endDateTime>
                        </requestedDates>
                        <roomRateDetails>
                            <roomInformation>
                                <roomRateIdentifier>
                                    <roomType>C1D</roomType>
                                    <ratePlanCode>RA3</ratePlanCode>
                                    <rateCategoryCode>PRO</rateCategoryCode>
                                    <rateQualifiedIndic>P</rateQualifiedIndic>
                                    <rateQualifiedIndic>A</rateQualifiedIndic>
                                </roomRateIdentifier>
                                <bookingCode>C1DRA3</bookingCode>
                                <guestCountDetails>
                                    <numberOfUnit>2</numberOfUnit>
                                    <unitQualifier>A</unitQualifier>
                                </guestCountDetails>
                            </roomInformation>
                            <specialInfo>
                                <freeTextDetails>
                                    <textSubjectQualifier>3</textSubjectQualifier>
                                    <informationType>BC</informationType>
                                    <source>M</source>
                                    <encoding>1</encoding>
                                </freeTextDetails>
                                <freeText>C1DRA3</freeText>
                            </specialInfo>
                            <specialInfo>
                                <freeTextDetails>
                                    <textSubjectQualifier>3</textSubjectQualifier>
                                    <informationType>RC</informationType>
                                    <source>M</source>
                                    <encoding>1</encoding>
                                </freeTextDetails>
                                <freeText>RA3</freeText>
                            </specialInfo>
                            <specialInfo>
                                <freeTextDetails>
                                    <textSubjectQualifier>3</textSubjectQualifier>
                                    <informationType>RO</informationType>
                                    <source>M</source>
                                    <encoding>1</encoding>
                                </freeTextDetails>
                                <freeText>C1D</freeText>
                            </specialInfo>
                            <bookingRequirement>
                                <guaranteeDepositStatusInfo>
                                    <statusDetails>
                                        <indicator>GUA</indicator>
                                        <action>UN</action>
                                    </statusDetails>
                                </guaranteeDepositStatusInfo>
                                <holdTime>
                                    <ruleDetails>
                                        <type>15</type>
                                        <quantity>19</quantity>
                                        <quantityUnit>2</quantityUnit>
                                    </ruleDetails>
                                </holdTime>
                                <paymentInformation>
                                    <paymentDetails>
                                        <formOfPaymentCode>1</formOfPaymentCode>
                                        <paymentType>1</paymentType>
                                        <serviceToPay>3</serviceToPay>
                                    </paymentDetails>
                                </paymentInformation>
                                <creditCardInformation>
                                    <formOfPayment>
                                        <type>CC</type>
                                        <vendorCode>AX</vendorCode>
                                    </formOfPayment>
                                </creditCardInformation>
                                <creditCardInformation>
                                    <formOfPayment>
                                        <type>CC</type>
                                        <vendorCode>CA</vendorCode>
                                    </formOfPayment>
                                </creditCardInformation>
                                <creditCardInformation>
                                    <formOfPayment>
                                        <type>CC</type>
                                        <vendorCode>DC</vendorCode>
                                    </formOfPayment>
                                </creditCardInformation>
                                <creditCardInformation>
                                    <formOfPayment>
                                        <type>CC</type>
                                        <vendorCode>EC</vendorCode>
                                    </formOfPayment>
                                </creditCardInformation>
                                <creditCardInformation>
                                    <formOfPayment>
                                        <type>CC</type>
                                        <vendorCode>IK</vendorCode>
                                    </formOfPayment>
                                </creditCardInformation>
                                <creditCardInformation>
                                    <formOfPayment>
                                        <type>CC</type>
                                        <vendorCode>EE</vendorCode>
                                    </formOfPayment>
                                </creditCardInformation>
                                <markerOfBookingRequirement></markerOfBookingRequirement>
                            </bookingRequirement>
                            <markerOfExtra></markerOfExtra>
                            <tariffInformation>
                                <tariffInfo>
                                    <amount>72.00</amount>
                                    <currency>EUR</currency>
                                    <dailyTotalIndicator>3</dailyTotalIndicator>
                                    <status>1</status>
                                </tariffInfo>
                            </tariffInformation>
                        </roomRateDetails>
                        <guaranteeOrDeposit>
                            <paymentInfo>
                                <paymentDetails>
                                    <formOfPaymentCode>1</formOfPaymentCode>
                                    <paymentType>2</paymentType>
                                    <serviceToPay>3</serviceToPay>
                                </paymentDetails>
                            </paymentInfo>
                            <groupCreditCardInfo>
                                <creditCardInfo>
                                    <ccInfo>
                                        <vendorCode>VI</vendorCode>
                                        <cardNumber>****************</cardNumber>
                                        <expiryDate>1227</expiryDate>
                                    </ccInfo>
                                </creditCardInfo>
                                <concealedCreditCardInfo>
                                    <ccInfo>
                                        <cardNumber>411111XXXXXX1116</cardNumber>
                                    </ccInfo>
                                </concealedCreditCardInfo>
                                <fortknoxIds>
                                    <referenceDetails>
                                        <type>NOX</type>
                                        <value>105398031323284</value>
                                    </referenceDetails>
                                </fortknoxIds>
                            </groupCreditCardInfo>
                        </guaranteeOrDeposit>
                        <guestList>
                            <occupantList>
                                <passengerReference>
                                    <type>ROP</type>
                                    <value>2</value>
                                </passengerReference>
                            </occupantList>
                        </guestList>
                    </requestableInformation>
                    <rateChanges>
                        <rateChangeAmountInformation>
                            <tariffInfo>
                                <amount>43.33</amount>
                                <currency>EUR</currency>
                                <dailyTotalIndicator>DY</dailyTotalIndicator>
                            </tariffInfo>
                        </rateChangeAmountInformation>
                        <rateChangePeriodInformation>
                            <businessSemantic>HRC</businessSemantic>
                            <beginDateTime>
                                <year>2025</year>
                                <month>6</month>
                                <day>4</day>
                            </beginDateTime>
                            <endDateTime>
                                <year>2025</year>
                                <month>6</month>
                                <day>5</day>
                            </endDateTime>
                        </rateChangePeriodInformation>
                    </rateChanges>
                </roomListInfo>
            </roomStayData>
        </Hotel_SellReply>
    </soap:Body>
</soap:Envelope>