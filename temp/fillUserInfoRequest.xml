
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ses="http://xml.amadeus.com/2010/06/Session_v3">
    <soap:Header>
        <ses:Session TransactionStatusCode="InSeries">
            <ses:SessionId>00DTTG7SC0</ses:SessionId>
            <ses:SequenceNumber>5</ses:SequenceNumber>
            <ses:SecurityToken>15GNKWHJBBABC1AZLQFPNOT47X</ses:SecurityToken>
        </ses:Session>
        <add:MessageID xmlns:add="http://www.w3.org/2005/08/addressing">55f3a78d-9971-4203-9721-dfa073f980cd</add:MessageID>
        <add:Action xmlns:add="http://www.w3.org/2005/08/addressing">http://webservices.amadeus.com/PNRADD_22_1_1A</add:Action>
        <add:To xmlns:add="http://www.w3.org/2005/08/addressing">https://noded5.test.webservices.amadeus.com/1ASIWVIELT4</add:To>
        <link:TransactionFlowLink xmlns:link="http://wsdl.amadeus.com/2010/06/ws/Link_v1" />
    </soap:Header>
    <soap:Body>
        <PNR_AddMultiElements>
            <pnrActions>
                <optionCode>0</optionCode>
            </pnrActions>
            <travellerInfo>
                <elementManagementPassenger>
                    <reference>
                        <qualifier>PR</qualifier>
                        <number>1</number>
                    </reference>
                    <segmentName>NM</segmentName>
                </elementManagementPassenger>
                <passengerData>
                    <travellerInformation>
                        <traveller>
                            <surname>NGUYEN</surname>
                        </traveller>
                        <passenger>
                            <firstName>VAN A</firstName>
                        </passenger>
                    </travellerInformation>
                </passengerData>
            </travellerInfo>
            <dataElementsMaster>
                <marker1 />
                <dataElementsIndiv>
                    <elementManagementData>
                        <reference>
                            <qualifier>OT</qualifier>
                            <number>1</number>
                        </reference>
                        <segmentName>AP</segmentName>
                    </elementManagementData>
                    <freetextData>
                        <freetextDetail>
                            <subjectQualifier>3</subjectQualifier>
                            <type>P07</type>
                        </freetextDetail>
                        <longFreetext>+84912345678</longFreetext>
                    </freetextData>
                    <referenceForDataElement>
                        <reference>
                            <qualifier>PR</qualifier>
                            <number>1</number>
                        </reference>
                    </referenceForDataElement>
                </dataElementsIndiv>
                <dataElementsIndiv>
                    <elementManagementData>
                        <reference>
                            <qualifier>OT</qualifier>
                            <number>2</number>
                        </reference>
                        <segmentName>AP</segmentName>
                    </elementManagementData>
                    <freetextData>
                        <freetextDetail>
                            <subjectQualifier>3</subjectQualifier>
                            <type>P02</type>
                        </freetextDetail>
                        <longFreetext><EMAIL></longFreetext>
                    </freetextData>
                    <referenceForDataElement>
                        <reference>
                            <qualifier>PR</qualifier>
                            <number>1</number>
                        </reference>
                    </referenceForDataElement>
                </dataElementsIndiv>
                <dataElementsIndiv>
                    <elementManagementData>
                        <reference>
                            <qualifier>OT</qualifier>
                            <number>3</number>
                        </reference>
                        <segmentName>OS</segmentName>
                    </elementManagementData>
                    <freetextData>
                        <freetextDetail>
                            <subjectQualifier>3</subjectQualifier>
                            <type>P27</type>
                        </freetextDetail>
                        <longFreetext>HIGH FLOOR REQUESTED</longFreetext>
                    </freetextData>
                    <referenceForDataElement>
                        <reference>
                            <qualifier>PR</qualifier>
                            <number>1</number>
                        </reference>
                    </referenceForDataElement>
                </dataElementsIndiv>
                <dataElementsIndiv>
                    <elementManagementData>
                        <reference>
                            <qualifier>OT</qualifier>
                            <number>4</number>
                        </reference>
                        <segmentName>TK</segmentName>
                    </elementManagementData>
                    <ticketElement>
                        <ticket>
                            <indicator>OK</indicator>
                        </ticket>
                    </ticketElement>
                </dataElementsIndiv>
                <dataElementsIndiv>
                    <elementManagementData>
                        <reference>
                            <qualifier>OT</qualifier>
                            <number>5</number>
                        </reference>
                        <segmentName>RF</segmentName>
                    </elementManagementData>
                    <freetextData>
                        <freetextDetail>
                            <subjectQualifier>3</subjectQualifier>
                            <type>P22</type>
                        </freetextDetail>
                        <longFreetext>AGENTNAME</longFreetext>
                    </freetextData>
                </dataElementsIndiv>
                
            </dataElementsMaster>
        </PNR_AddMultiElements>
    </soap:Body>
</soap:Envelope>