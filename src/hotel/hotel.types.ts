export enum AgeQualifyingCode {
  ADULT = '10',
  CHILD = '8',
  INFANT = '6'
}

export interface Warning {
  Type: string
  Status: string
  Tag: string
}

export enum RoomCategory {
  ACCESSIBLE = 'H',
  BUDGET = 'I',
  BUSINESS = 'B',
  COMFORT = 'G',
  DELUXE = 'D',
  DUPLEX = 'X',
  EXECUTIVE = 'E',
  CONCIERGE_SUITE = 'C',
  FAMILY = 'F',
  JUNIOR_SUITE = 'S',
  PENTHOUSE = 'P',
  PREMIER = 'K',
  RESIDENTIAL = 'R',
  STANDARD = 'M',
  STUDIO = 'L',
  SUPERIOR = 'A',
  VILLA = 'V',
  UNKNOWN = '*'
}

export enum BedType {
  DOUBLE = 'D',
  KING = 'K',
  PULL_OUT = 'P',
  QUEEN = 'Q',
  SINGLE = 'S',
  TWIN = 'T',
  WATER = 'W',
  VARIABLE = '*'
}

export const RoomCategoryNames: Record<string, string> = {
  [RoomCategory.ACCESSIBLE]: 'Accessible Room',
  [RoomCategory.BUDGET]: 'Budget Room',
  [RoomCategory.BUSINESS]: 'Business Room',
  [RoomCategory.COMFORT]: 'Comfort Room',
  [RoomCategory.DELUXE]: 'Deluxe Room',
  [RoomCategory.DUPLEX]: 'Duplex',
  [RoomCategory.EXECUTIVE]: 'Executive Room',
  [RoomCategory.CONCIERGE_SUITE]: 'Concierge/Executive Suite',
  [RoomCategory.FAMILY]: 'Family Room',
  [RoomCategory.JUNIOR_SUITE]: 'Junior Suite/Mini Suite',
  [RoomCategory.PENTHOUSE]: 'Penthouse',
  [RoomCategory.PREMIER]: 'Premier Room',
  [RoomCategory.RESIDENTIAL]: 'Residential Apartment',
  [RoomCategory.STANDARD]: 'Standard Room',
  [RoomCategory.STUDIO]: 'Studio',
  [RoomCategory.SUPERIOR]: 'Superior Room',
  [RoomCategory.VILLA]: 'Villa',
  [RoomCategory.UNKNOWN]: 'Unknown Room Type'
}

export const BedTypeNames: Record<string, string> = {
  [BedType.DOUBLE]: 'Double Bed',
  [BedType.KING]: 'King Size Bed',
  [BedType.PULL_OUT]: 'Pull-out Bed',
  [BedType.QUEEN]: 'Queen Size Bed',
  [BedType.SINGLE]: 'Single Bed',
  [BedType.TWIN]: 'Twin Bed',
  [BedType.WATER]: 'Water Bed',
  [BedType.VARIABLE]: 'Variable Bed Type'
}

export interface ParsedRoomTypeInfo {
  roomCategory: string
  roomCategoryName: string
  numberOfBeds: number
  bedType: string
  bedTypeName: string
  fullDescription: string
}

/**
 *
 * @param roomTypeCode room type code (example: "M2D", "A1K")
 * @returns room data
 */
export function parseRoomTypeCode(roomTypeCode: string): ParsedRoomTypeInfo {
  if (!roomTypeCode || roomTypeCode.length !== 3) {
    return {
      roomCategory: RoomCategory.UNKNOWN,
      roomCategoryName: RoomCategoryNames[RoomCategory.UNKNOWN],
      numberOfBeds: 0,
      bedType: BedType.VARIABLE,
      bedTypeName: BedTypeNames[BedType.VARIABLE],
      fullDescription: 'Unknown Room'
    }
  }

  const roomCategory = roomTypeCode.charAt(0)
  const numberOfBeds = parseInt(roomTypeCode.charAt(1), 10)
  const bedType = roomTypeCode.charAt(2)

  const roomCategoryName =
    RoomCategoryNames[roomCategory] || RoomCategoryNames[RoomCategory.UNKNOWN]
  const bedTypeName = BedTypeNames[bedType] || BedTypeNames[BedType.VARIABLE]

  const bedsText = numberOfBeds > 1 ? `${numberOfBeds} ${bedTypeName}s` : bedTypeName
  const fullDescription = `${roomCategoryName} with ${bedsText}`

  return {
    roomCategory,
    roomCategoryName,
    numberOfBeds: isNaN(numberOfBeds) ? 0 : numberOfBeds,
    bedType,
    bedTypeName,
    fullDescription
  }
}

/**
 * get short description of room type code
 * @param roomTypeCode room type code (example: "M2D", "A1K")
 * @returns short description
 */
export function getRoomTypeDescription(roomTypeCode: string): string {
  const info = parseRoomTypeCode(roomTypeCode)
  return info.fullDescription
}

/**
 * enum for payment card code
 * payment card code according to Amadeus standard
 */
export enum PaymentCardCode {
  AMERICAN_EXPRESS = 'AX',
  MASTERCARD = 'CA',
  DINERS_CLUB = 'DC',
  DISCOVER = 'DS',
  EUROCARD = 'EC',
  JCB = 'JC',
  VISA = 'VI',
  IKEA = 'IK',
  MAESTRO = 'MA',
  ELECTRON = 'EE',
  UATP = 'TP',
  CHINA_UNION_PAY = 'CU',
  CARTE_BLANCHE = 'CB',
  OTHER = 'OT'
}

/**
 * display name for payment card code
 */
export const PaymentCardNames: Record<string, string> = {
  [PaymentCardCode.AMERICAN_EXPRESS]: 'American Express',
  [PaymentCardCode.MASTERCARD]: 'MasterCard',
  [PaymentCardCode.DINERS_CLUB]: 'Diners Club',
  [PaymentCardCode.DISCOVER]: 'Discover',
  [PaymentCardCode.EUROCARD]: 'Eurocard',
  [PaymentCardCode.JCB]: 'JCB',
  [PaymentCardCode.VISA]: 'Visa',
  [PaymentCardCode.IKEA]: 'IKEA',
  [PaymentCardCode.MAESTRO]: 'Maestro',
  [PaymentCardCode.ELECTRON]: 'Visa Electron',
  [PaymentCardCode.UATP]: 'Universal Air Travel Plan',
  [PaymentCardCode.CHINA_UNION_PAY]: 'China Union Pay',
  [PaymentCardCode.CARTE_BLANCHE]: 'Carte Blanche',
  [PaymentCardCode.OTHER]: 'Other Card'
}

/**
 * get display name for payment card code
 * @param cardCode payment card code
 * @returns display name
 */
export function getPaymentCardName(cardCode: string): string {
  return PaymentCardNames[cardCode] || 'Unknown Card'
}

/**
 * image categories
 */
export const ImageCategories: Record<string, string> = {
  '1': 'Exterior view',
  '2': 'Lobby view',
  '3': 'Pool view',
  '4': 'Restaurant',
  '5': 'Health club',
  '6': 'Guest room',
  '7': 'Suite',
  '8': 'Meeting room',
  '9': 'Ballroom',
  '10': 'Golf course',
  '11': 'Beach',
  '12': 'Spa',
  '13': 'Bar/Lounge',
  '14': 'Recreational facility',
  '15': 'Logo',
  '16': 'Basics',
  '17': 'Map',
  '18': 'Promotional',
  '19': 'Hot news',
  '20': 'Miscellaneous',
  '21': 'Guest room amenity',
  '22': 'Property amenity',
  '23': 'Business center'
}
