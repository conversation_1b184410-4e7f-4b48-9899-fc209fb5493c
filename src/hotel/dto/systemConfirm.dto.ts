import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, IsNumberString } from 'class-validator'

export class SystemConfirmDto {
  @ApiProperty({
    description: 'The session id from previous booking',
    example: '03KYBGFIL5'
  })
  @IsNotEmpty()
  @IsString()
  sessionId: string

  @ApiProperty({
    description: 'The sequence number from previous booking',
    example: '6'
  })
  @IsNotEmpty()
  @IsString()
  @IsNumberString()
  sequenceNumber: string

  @ApiProperty({
    description: 'The security token from previous booking',
    example: '1O1L6FKTCRO63QD6NCG6VH2EE'
  })
  @IsNotEmpty()
  @IsString()
  securityToken: string
}
