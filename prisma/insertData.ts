import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import { parse } from 'csv-parse'
import { createReadStream } from 'fs'
import { join } from 'path'

const prisma = new PrismaClient()

async function importCSV() {
  const chains = new Set()
  const countries = new Set()
  const hotels = []
  const cities = new Set()

  // Parse CSV
  const filePath = join(__dirname, 'hotels.csv')
  createReadStream(filePath)
    .pipe(parse({ columns: true }))
    .on('data', (row) => {
      // Collect unique chains
      chains.add(
        JSON.stringify({
          chainCode: row.CHAIN,
          chainName: row.CHAIN_NAME
        })
      )

      // Collect unique countries
      countries.add(
        JSON.stringify({
          countryCode: row.COUNTRY_CODE,
          countryName: row.COUNTRY_NAME
        })
      )

      // Collect hotel data
      hotels.push({
        propertyCode: row.PROPERTY_CODE,
        city: row.CITY,
        propertyIdentifier: row.PROPERTY_IDENTIFIER,
        propertyName: row.PROPERTY_NAME,
        location: row.LOCATION,
        transportation: row.TRANSPORTATION,
        addressLine1: row.ADRESS_LINE_1,
        addressLine2: row.ADRESS_LINE_2 || null,
        city2: row.CITY2,
        stateCode: row.STATE_CODE || null,
        zipCode: row.ZIP_CODE || null,
        phone: row.PHONE,
        fax: row.FAX || null,
        localRating: row.LOCALRATING ? parseInt(row.LOCALRATING) : null,
        selfRating: row.SELFRATING || null,
        latitude: parseFloat(row.LATITUDE),
        longitude: parseFloat(row.LONGITUDE),
        dupePoolId: row.DUPE_POOL_ID,
        providerValue: row.PROVIDER_VALUE || null,
        lastUpdateTimestamp:
          row.LAST_UPDATE_TIMESTAMP.split('/').length === 3
            ? new Date(row.LAST_UPDATE_TIMESTAMP.split('/').reverse().join('-'))
            : new Date('1900-01-01'),
        chainCode: row.CHAIN,
        countryCode: row.COUNTRY_CODE
      })
      if (row.LAST_UPDATE_TIMESTAMP.split('/').length < 3) {
        console.log(row)
      }

      // collect cities
      cities.add(
        JSON.stringify({
          cityCode: row.CITY,
          cityName: row.CITY2,
          countryCode: row.COUNTRY_CODE
        })
      )
    })
    .on('end', async () => {
      // Insert chains
      await prisma.chain.createMany({
        data: Array.from(chains).map((c) => JSON.parse(c as string)),
        skipDuplicates: true
      })

      // Insert countries
      await prisma.country.createMany({
        data: Array.from(countries).map((c) => JSON.parse(c as string)),
        skipDuplicates: true
      })

      // Insert cities
      await prisma.city.createMany({
        data: Array.from(cities).map((c) => JSON.parse(c as string)),
        skipDuplicates: true
      })

      // Insert hotels
      await prisma.hotel.createMany({
        data: hotels,
        skipDuplicates: true
      })

      console.log('Data imported successfully')
      await prisma.$disconnect()
    })
}

importCSV().catch(console.error)
