import { Injectable, NestMiddleware, ServiceUnavailableException } from '@nestjs/common'

@Injectable()
export class DatabaseErrorMiddleware implements NestMiddleware {
  use(req: any, res: any, next: () => void) {
    try {
      next()
    } catch (error) {
      if (error.code === 'P2024' || error.message.includes('Connection pool')) {
        throw new ServiceUnavailableException('Database connection limit reached')
      }
      throw error
    }
  }
}
