import { ApiProperty } from '@nestjs/swagger'
import { <PERSON>Date, IsEmail, IsEthereumAddress, IsNotEmpty, IsNumber, IsString } from 'class-validator'

export class GetMsgDto {
  @IsNotEmpty()
  @IsString()
  @IsEthereumAddress()
  @ApiProperty()
  address: string
}

export class GetMsgResp {
  message: string
  address: string
}

export class SigninWithGmailDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  email: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  token: string
}

export class SigninWithPasswordDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  email: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  password: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  deviceId: string
}

export class SigninResp {
  message: string
  token: string
}
export class RegisterDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  email: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  password: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty()
  @IsNotEmpty()
  @IsDate()
  birthDate: Date

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  gender: number

  @ApiProperty()
  @IsNumber()
  height: number

  @ApiProperty()
  @IsNumber()
  weight: number

  @ApiProperty()
  @IsString()
  avatar: string

  @ApiProperty()
  @IsString()
  referralCode?: string

  @ApiProperty()
  @IsString()
  firstName?: string

  @ApiProperty()
  @IsString()
  lastName?: string
}
export class SigninDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  email: string

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  password: string
}
