import { PrismaClient, Prisma } from '@prisma/client'
import { parse } from 'csv-parse'
import { createReadStream } from 'fs'
import { join } from 'path'

const prisma = new PrismaClient()

async function importCSV() {
  const countries = new Set()

  // Parse CSV
  const filePath = join(__dirname, '..', '..', 'prisma', 'sql', 'countryCode.csv')
  createReadStream(filePath)
    .pipe(parse({ columns: true }))
    .on('data', (row) => {
      countries.add(
        JSON.stringify({
          countryCode: row.Code,
          countryName: row.Name
        })
      )
    })
    .on('end', async () => {
      const countryData = Array.from(countries).map((c) => JSON.parse(c as string))
      
      console.log(`Found ${countryData.length} countries to import`)
      
      try {
        // Insert countries into the Country table
        await prisma.country.createMany({
          data: countryData,
          skipDuplicates: true
        })
        
        console.log('Country data imported successfully')
      } catch (error) {
        console.error('Error importing country data:', error)
      } finally {
        await prisma.$disconnect()
      }
    })
    .on('error', (error) => {
      console.error('Error parsing CSV:', error)
      prisma.$disconnect()
    })
}

importCSV().catch((error) => {
  console.error('Unhandled error:', error)
  prisma.$disconnect()
})
