import { google } from 'googleapis'
import { Injectable } from '@nestjs/common'
import MailComposer = require('nodemailer/lib/mail-composer')
import * as auth from './mail.auth'
import { compile } from 'handlebars'
import { readFileSync } from 'fs'
import { join } from 'path'
import {
  ForgetPasswordMail,
  InviteMailData,
  MailLanguage,
  MailType,
  PaymentMail,
  VerifyMail
} from './mail.types'
import { templates } from './templates/templates'
import axios from 'axios'

const gmail = google.gmail('v1')

@Injectable()
export class MailService {
  constructor() {}

  async sendMail(
    type: MailType,
    lang: MailLanguage,
    txData: PaymentMail | VerifyMail | ForgetPasswordMail | InviteMailData
  ) {
    const mailType = templates.find((f) => f.type == type && f.language == lang)
    const filePath = join(__dirname, `../../templates/${mailType.contentFile}`)
    const template = readFileSync(filePath, { encoding: 'utf8' })
    const content = compile(template)

    const emailContent = txData
    return await this.sendMessage({
      to: emailContent.email,
      subject: mailType.title,
      html: content(emailContent)
    })
  }

  async sendVerifyMail(email: string, code: string) {
    const mailType = templates.find((f) => f.type == 'verify' && f.language == 'en')
    const filePath = join(__dirname, `../../templates/${mailType.contentFile}`)
    const template = readFileSync(filePath, { encoding: 'utf8' })
    const content = compile(template)

    const emailContent = {
      email,
      code
    }

    return await this.sendMessage({
      to: emailContent.email,
      subject: mailType.title,
      html: content(emailContent),
      emailIndex: 1
    })
    // return await this.mailgunSend({
    //   to: emailContent.email,
    //   subject: mailType.title,
    //   html: content(emailContent)
    // })
  }

  async sendMessage({
    to,
    subject = '',
    html = '',
    attachments = [],
    emailIndex = 0
  }: {
    to: string
    subject?: string
    html?: string
    attachments?: any[]
    emailIndex?: number
  }) {
    const authenticated = await auth.authorize(emailIndex)
    if (!authenticated) {
      throw 'No Authenticated'
    }
    // build and encode the mail

    const buildMessage = () =>
      new Promise<string>((resolve, reject) => {
        const message = new MailComposer({
          to,
          subject,
          html,
          attachments,
          textEncoding: 'base64'
        })

        message.compile().build((err, msg) => {
          if (err) {
            reject(err)
          }

          const encodedMessage = Buffer.from(msg)
            .toString('base64')
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=+$/, '')

          resolve(encodedMessage)
        })
      })

    const encodedMessage = await buildMessage()
    await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: encodedMessage
      }
    })
  }

  async mailgunSend({
    to,
    subject = '',
    html = ''
  }: {
    to: string
    subject?: string
    html?: string
  }) {
    const form = new FormData()
    form.append('from', 'RunX <<EMAIL>>')
    form.append('to', to)
    form.append('subject', subject)
    form.append('html', html)

    const domainName = 'mail.runx.app'
    try {
      const resp = await axios.post(`https://api.mailgun.net/v3/${domainName}/messages`, form, {
        auth: {
          username: 'api',
          password: '' //MAILGUN_API_KEY
        },
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return resp.data
    } catch (error) {
      console.log(error)
      return error
    }
  }
}
