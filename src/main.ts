//import './apmService'
import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { setAppSetting } from './app.settings'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { ConfigService } from '@nestjs/config'
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify'

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter())
  setAppSetting(app)
  const version = '1.0'
  const config = new DocumentBuilder()
    .setTitle('run X API')
    .setDescription('run X API description')
    .setVersion(version)
    .addBearerAuth(
      {
        type: 'apiKey',
        scheme: 'JWT',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Type into the text box: Bearer {your JWT token}',
        in: 'header'
      },
      'JWT'
    )
    .build()
  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('api/docs', app, document)
  const configService = app.get(ConfigService)
  const port = configService.get('APP_PORT')
  const server = await app.listen(port)
  server.setTimeout(30000) // 30 seconds
  console.log(`app started with ${port}`)
}
bootstrap()
