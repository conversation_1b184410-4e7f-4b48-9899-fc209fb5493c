<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ses="http://xml.amadeus.com/2010/06/Session_v3">
    <soap:Header>
        <ses:Session TransactionStatusCode="InSeries">
            <ses:SessionId>05QTXIRFZT</ses:SessionId>
            <ses:SequenceNumber>6</ses:SequenceNumber>
            <ses:SecurityToken>28UV54QRPEFK82YVWXRIEXVT2L</ses:SecurityToken>
        </ses:Session>
        <add:MessageID xmlns:add="http://www.w3.org/2005/08/addressing">3b3c79b0-cb00-481a-86cc-8a0526041db0</add:MessageID>
        <add:Action xmlns:add="http://www.w3.org/2005/08/addressing">http://webservices.amadeus.com/PNRADD_22_1_1A</add:Action>
        <add:To xmlns:add="http://www.w3.org/2005/08/addressing">https://noded5.test.webservices.amadeus.com/1ASIWVIELT4</add:To>
        <link:TransactionFlowLink xmlns:link="http://wsdl.amadeus.com/2010/06/ws/Link_v1" />
    </soap:Header>
    <soap:Body>
        <PNR_AddMultiElements>
            <pnrActions>
                <optionCode>11</optionCode>
                <optionCode>30</optionCode>
            </pnrActions>
        </PNR_AddMultiElements>
    </soap:Body>
</soap:Envelope>