import { google } from 'googleapis'
import {
  GOOGLE_AUTH_REDIRECT,
  GOO<PERSON>LE_MAILER_CLIENT_ID,
  GOOGLE_MAILER_CLIENT_SECRET,
  GOOGLE_MAILER_REFRESH_TOKEN
} from '../app.settings'
import { join } from 'path'
import * as fs from 'fs-extra'

const SCOPES = [
  'https://www.googleapis.com/auth/gmail.readonly',
  // 'https://www.googleapis.com/auth/gmail.modify',
  // 'https://www.googleapis.com/auth/gmail.compose',
  'https://www.googleapis.com/auth/gmail.send'
]
const TOKEN_PATH = join(__dirname, '../mail/credentials/token.json')

// index: 0: znode mail, 1: no-reply mail
export const authorize = async (index: number) => {
  // check if the token already exists
  const token = {
    type: 'authorized_user',
    client_id: GOOGLE_MAILER_CLIENT_ID,
    client_secret: GOOG<PERSON>_MAILER_CLIENT_SECRET,
    refresh_token: GOOGLE_MAILER_REFRESH_TOKEN
  }

  if (token) {
    authenticate(token, index)
    return true
  }

  return false
}

export const getNewToken = async (index: number) => {
  const oAuth2Client = getOAuth2Client(index)

  return oAuth2Client.generateAuthUrl({
    access_type: 'offline',
    prompt: 'consent',
    scope: SCOPES
  })
}

export const saveToken = async (token: any) => {
  await fs.writeFile(TOKEN_PATH, JSON.stringify(token))
}

const getOAuth2Client = (index: number) => {
  const oAuth2Client = new google.auth.OAuth2(
    GOOGLE_MAILER_CLIENT_ID,
    GOOGLE_MAILER_CLIENT_SECRET,
    GOOGLE_AUTH_REDIRECT
  )
  return oAuth2Client
}

const authenticate = (token: any, index: number) => {
  const oAuth2Client = getOAuth2Client(index)

  oAuth2Client.setCredentials(token)
  google.options({
    auth: oAuth2Client
  })
}
