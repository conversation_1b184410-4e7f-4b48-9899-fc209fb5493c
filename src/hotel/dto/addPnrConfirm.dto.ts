import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsNotEmpty, IsString, IsBoolean, IsOptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator'

export class AddPnrConfirmDto {
  @ApiProperty({
    description: 'The session id from previous search',
    example: '02JV3Q93R0'
  })
  @IsNotEmpty()
  @IsString()
  sessionId: string

  @ApiProperty({
    description: 'The sequence number from previous search',
    example: '6'
  })
  @IsNotEmpty()
  @IsString()
  sequenceNumber: string

  @ApiProperty({
    description: 'The security token from previous search',
    example: '1YVE4IWPFJ8NQ3MECH1J0L8KH0'
  })
  @IsNotEmpty()
  @IsString()
  securityToken: string
}