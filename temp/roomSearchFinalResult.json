{"sessionId": "00DTTG7SC0", "sequenceNumber": "1", "securityToken": "15GNKWHJBBABC1AZLQFPNOT47X", "hotel": {"hotelCode": "RTMADCMP", "hotelName": "NOVOTEL MADRID CAMPO NACIONES", "hotelCityCode": "MAD", "chainCode": "RT", "chainName": "ACCOR HOTELS", "hotelCodeContext": "1A", "address": {"addressline": "CALLE AMSTERDAM, 3", "cityname": "MADRID", "postalcode": "28042", "countryname": {"Code": "ES"}}, "location": {"Latitude": "4046248", "Longitude": "-361447"}, "contactNumber": [{"PhoneTechType": "1", "PhoneNumber": "34/91/7211818"}, {"PhoneTechType": "3", "PhoneNumber": "34/91/7211122"}], "rating": "4", "amenities": [{"amenityCode": "33", "amenityName": "Elevators", "description": ""}, {"amenityCode": "79", "amenityName": "Sauna", "description": ""}, {"amenityCode": "77", "amenityName": "Room service", "description": ""}, {"amenityCode": "115", "amenityName": "220 AC", "description": ""}, {"amenityCode": "55", "amenityName": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, {"amenityCode": "179", "amenityName": "Wireless internet in public areas", "description": ""}, {"amenityCode": "15", "amenityName": "Car rental desk", "description": ""}, {"amenityCode": "218", "amenityName": "Children welcome", "description": ""}, {"amenityCode": "37", "amenityName": "Express check-out", "description": ""}, {"amenityCode": "41", "amenityName": "Free airport shuttle", "description": ""}, {"amenityCode": "83", "amenityName": "Solarium", "description": ""}, {"amenityCode": "103", "amenityName": "Multilingual staff", "description": ""}, {"amenityCode": "61", "amenityName": "Massage services", "description": ""}, {"amenityCode": "66", "amenityName": "Outdoor pool", "description": ""}, {"amenityCode": "223", "amenityName": "Internet services", "description": ""}, {"amenityCode": "68", "amenityName": "Parking", "description": ""}, {"amenityCode": "224", "amenityName": "Pets allowed", "description": ""}, {"amenityCode": "92", "amenityName": "Translation services", "description": ""}, {"amenityCode": "91", "amenityName": "Tour/sightseeing desk", "description": ""}, {"amenityCode": "165", "amenityName": "Lounges/bars", "description": ""}, {"amenityCode": "28", "amenityName": "Doctor on call", "description": ""}, {"amenityCode": "1", "amenityName": "24-hour front desk", "description": ""}, {"amenityCode": "4", "amenityName": "Adjoining rooms", "description": ""}, {"amenityCode": "53", "amenityName": "Indoor parking", "description": ""}, {"amenityCode": "76", "amenityName": "Restaurant", "description": ""}], "images": {"Exterior view": [{"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/8D973548CB0C4AB58EBE405F2D62536A.JPEG", "caption": "Exterior view", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "8D973548CB0C4AB58EBE405F2D62536A", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/A.JPEG", "caption": "Exterior view", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/B.JPEG", "caption": "Exterior view", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/C.JPEG", "caption": "Exterior view", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/D.JPEG", "caption": "Exterior view", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/E.JPEG", "caption": "Exterior view", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/F.JPEG", "caption": "Exterior view", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/G.JPEG", "caption": "Exterior view", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/H.JPEG", "caption": "Exterior view", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/I.JPEG", "caption": "Exterior view", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/J.JPEG", "caption": "Exterior view", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/8D973548CB0C4AB58EBE405F2D62536A/K.JPEG", "caption": "Exterior view", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/1D7009865E5F499CB6DF2D1A035BEA86.JPEG", "caption": "Exterior view", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "1D7009865E5F499CB6DF2D1A035BEA86", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/A.JPEG", "caption": "Exterior view", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/B.JPEG", "caption": "Exterior view", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/C.JPEG", "caption": "Exterior view", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/D.JPEG", "caption": "Exterior view", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/E.JPEG", "caption": "Exterior view", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/F.JPEG", "caption": "Exterior view", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/G.JPEG", "caption": "Exterior view", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/H.JPEG", "caption": "Exterior view", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/I.JPEG", "caption": "Exterior view", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/J.JPEG", "caption": "Exterior view", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/K.JPEG", "caption": "Exterior view", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/9ABD87342D7D4689A4123424F0328999.JPEG", "caption": "Exterior view", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "9ABD87342D7D4689A4123424F0328999", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/A.JPEG", "caption": "Exterior view", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/B.JPEG", "caption": "Exterior view", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/C.JPEG", "caption": "Exterior view", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/D.JPEG", "caption": "Exterior view", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/E.JPEG", "caption": "Exterior view", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/F.JPEG", "caption": "Exterior view", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/G.JPEG", "caption": "Exterior view", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/H.JPEG", "caption": "Exterior view", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/I.JPEG", "caption": "Exterior view", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/J.JPEG", "caption": "Exterior view", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9ABD87342D7D4689A4123424F0328999/K.JPEG", "caption": "Exterior view", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/AE0A467AA3F242948E57DD3CCF79B14E.JPEG", "caption": "Exterior view", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "AE0A467AA3F242948E57DD3CCF79B14E", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/A.JPEG", "caption": "Exterior view", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/B.JPEG", "caption": "Exterior view", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/C.JPEG", "caption": "Exterior view", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/D.JPEG", "caption": "Exterior view", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/E.JPEG", "caption": "Exterior view", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/F.JPEG", "caption": "Exterior view", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/G.JPEG", "caption": "Exterior view", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/H.JPEG", "caption": "Exterior view", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/I.JPEG", "caption": "Exterior view", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/J.JPEG", "caption": "Exterior view", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/AE0A467AA3F242948E57DD3CCF79B14E/K.JPEG", "caption": "Exterior view", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}], "Miscellaneous": [{"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/1FF827F15600428EB19FD9ABAB39A1B0.JPEG", "caption": "Family", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "1FF827F15600428EB19FD9ABAB39A1B0", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/A.JPEG", "caption": "Family", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/B.JPEG", "caption": "Family", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/C.JPEG", "caption": "Family", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/D.JPEG", "caption": "Family", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/E.JPEG", "caption": "Family", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/F.JPEG", "caption": "Family", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/G.JPEG", "caption": "Family", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/H.JPEG", "caption": "Family", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/I.JPEG", "caption": "Family", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/J.JPEG", "caption": "Family", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1FF827F15600428EB19FD9ABAB39A1B0/K.JPEG", "caption": "Family", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}], "Meeting room": [{"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/C4E550C2153D4A5F95353B7B7102A92C.JPEG", "caption": "Meeting room", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "C4E550C2153D4A5F95353B7B7102A92C", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/A.JPEG", "caption": "Meeting room", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/B.JPEG", "caption": "Meeting room", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/C.JPEG", "caption": "Meeting room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/D.JPEG", "caption": "Meeting room", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/E.JPEG", "caption": "Meeting room", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/F.JPEG", "caption": "Meeting room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/G.JPEG", "caption": "Meeting room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/H.JPEG", "caption": "Meeting room", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/I.JPEG", "caption": "Meeting room", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/J.JPEG", "caption": "Meeting room", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C4E550C2153D4A5F95353B7B7102A92C/K.JPEG", "caption": "Meeting room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/6AC13D58769740429F6F21CB6C1B0611.JPEG", "caption": "Meeting room", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "6AC13D58769740429F6F21CB6C1B0611", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/A.JPEG", "caption": "Meeting room", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/B.JPEG", "caption": "Meeting room", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/C.JPEG", "caption": "Meeting room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/D.JPEG", "caption": "Meeting room", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/E.JPEG", "caption": "Meeting room", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/F.JPEG", "caption": "Meeting room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/G.JPEG", "caption": "Meeting room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/H.JPEG", "caption": "Meeting room", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/I.JPEG", "caption": "Meeting room", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/J.JPEG", "caption": "Meeting room", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AC13D58769740429F6F21CB6C1B0611/K.JPEG", "caption": "Meeting room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/3F54E5BD2664486CBE760A4117484845.JPEG", "caption": "Meeting room", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "3F54E5BD2664486CBE760A4117484845", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/A.JPEG", "caption": "Meeting room", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/B.JPEG", "caption": "Meeting room", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/C.JPEG", "caption": "Meeting room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/D.JPEG", "caption": "Meeting room", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/E.JPEG", "caption": "Meeting room", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/F.JPEG", "caption": "Meeting room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/G.JPEG", "caption": "Meeting room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/H.JPEG", "caption": "Meeting room", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/I.JPEG", "caption": "Meeting room", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/J.JPEG", "caption": "Meeting room", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3F54E5BD2664486CBE760A4117484845/K.JPEG", "caption": "Meeting room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}], "Bar/Lounge": [{"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/C1157121D15141C5A02F7F3A2ECFF910.JPEG", "caption": "Bar Lounge", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "C1157121D15141C5A02F7F3A2ECFF910", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/A.JPEG", "caption": "Bar Lounge", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/B.JPEG", "caption": "Bar Lounge", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/C.JPEG", "caption": "Bar Lounge", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/D.JPEG", "caption": "Bar Lounge", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/E.JPEG", "caption": "Bar Lounge", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/F.JPEG", "caption": "Bar Lounge", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/G.JPEG", "caption": "Bar Lounge", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/H.JPEG", "caption": "Bar Lounge", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/I.JPEG", "caption": "Bar Lounge", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/J.JPEG", "caption": "Bar Lounge", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/C1157121D15141C5A02F7F3A2ECFF910/K.JPEG", "caption": "Bar Lounge", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/5A0A38009A664BD1A7EED6C6A1EFD007.JPEG", "caption": "Bar Lounge", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "5A0A38009A664BD1A7EED6C6A1EFD007", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/A.JPEG", "caption": "Bar Lounge", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/B.JPEG", "caption": "Bar Lounge", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/C.JPEG", "caption": "Bar Lounge", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/D.JPEG", "caption": "Bar Lounge", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/E.JPEG", "caption": "Bar Lounge", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/F.JPEG", "caption": "Bar Lounge", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/G.JPEG", "caption": "Bar Lounge", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/H.JPEG", "caption": "Bar Lounge", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/I.JPEG", "caption": "Bar Lounge", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/J.JPEG", "caption": "Bar Lounge", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/5A0A38009A664BD1A7EED6C6A1EFD007/K.JPEG", "caption": "Bar Lounge", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}], "Recreational facility": [{"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/E58BC12EE5E345028877EB522659A06E.JPEG", "caption": "Recreational facility", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "E58BC12EE5E345028877EB522659A06E", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/A.JPEG", "caption": "Recreational facility", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/B.JPEG", "caption": "Recreational facility", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/C.JPEG", "caption": "Recreational facility", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/D.JPEG", "caption": "Recreational facility", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/E.JPEG", "caption": "Recreational facility", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/F.JPEG", "caption": "Recreational facility", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/G.JPEG", "caption": "Recreational facility", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/H.JPEG", "caption": "Recreational facility", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/I.JPEG", "caption": "Recreational facility", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/J.JPEG", "caption": "Recreational facility", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/E58BC12EE5E345028877EB522659A06E/K.JPEG", "caption": "Recreational facility", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/684D21A7FD93486EB65F13C267CD7AA8.JPEG", "caption": "Recreational facility", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "684D21A7FD93486EB65F13C267CD7AA8", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/A.JPEG", "caption": "Recreational facility", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/B.JPEG", "caption": "Recreational facility", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/C.JPEG", "caption": "Recreational facility", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/D.JPEG", "caption": "Recreational facility", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/E.JPEG", "caption": "Recreational facility", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/F.JPEG", "caption": "Recreational facility", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/G.JPEG", "caption": "Recreational facility", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/H.JPEG", "caption": "Recreational facility", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/I.JPEG", "caption": "Recreational facility", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/J.JPEG", "caption": "Recreational facility", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/684D21A7FD93486EB65F13C267CD7AA8/K.JPEG", "caption": "Recreational facility", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/1BF45962885942D8B50369D32EEEDDBC.JPEG", "caption": "Recreational facility", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "1BF45962885942D8B50369D32EEEDDBC", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/A.JPEG", "caption": "Recreational facility", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/B.JPEG", "caption": "Recreational facility", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/C.JPEG", "caption": "Recreational facility", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/D.JPEG", "caption": "Recreational facility", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/E.JPEG", "caption": "Recreational facility", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/F.JPEG", "caption": "Recreational facility", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/G.JPEG", "caption": "Recreational facility", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/H.JPEG", "caption": "Recreational facility", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/I.JPEG", "caption": "Recreational facility", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/J.JPEG", "caption": "Recreational facility", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/1BF45962885942D8B50369D32EEEDDBC/K.JPEG", "caption": "Recreational facility", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/6AFC5A71137A438EA60B0170BC157252.JPEG", "caption": "Recreational facility", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "6AFC5A71137A438EA60B0170BC157252", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/A.JPEG", "caption": "Recreational facility", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/B.JPEG", "caption": "Recreational facility", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/C.JPEG", "caption": "Recreational facility", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/D.JPEG", "caption": "Recreational facility", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/E.JPEG", "caption": "Recreational facility", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/F.JPEG", "caption": "Recreational facility", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/G.JPEG", "caption": "Recreational facility", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/H.JPEG", "caption": "Recreational facility", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/I.JPEG", "caption": "Recreational facility", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/J.JPEG", "caption": "Recreational facility", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/6AFC5A71137A438EA60B0170BC157252/K.JPEG", "caption": "Recreational facility", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}], "Restaurant": [{"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/ABB843B3CC4F42E08A905996DEA20D9F.JPEG", "caption": "Restaurant", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "ABB843B3CC4F42E08A905996DEA20D9F", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/A.JPEG", "caption": "Restaurant", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/B.JPEG", "caption": "Restaurant", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/C.JPEG", "caption": "Restaurant", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/D.JPEG", "caption": "Restaurant", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/E.JPEG", "caption": "Restaurant", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/F.JPEG", "caption": "Restaurant", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/G.JPEG", "caption": "Restaurant", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/H.JPEG", "caption": "Restaurant", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/I.JPEG", "caption": "Restaurant", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/J.JPEG", "caption": "Restaurant", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/ABB843B3CC4F42E08A905996DEA20D9F/K.JPEG", "caption": "Restaurant", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/9CD1FFB7FC9542C6ABCA97329E1E9B8F.JPEG", "caption": "Restaurant", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "9CD1FFB7FC9542C6ABCA97329E1E9B8F", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/A.JPEG", "caption": "Restaurant", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/B.JPEG", "caption": "Restaurant", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/C.JPEG", "caption": "Restaurant", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/D.JPEG", "caption": "Restaurant", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/E.JPEG", "caption": "Restaurant", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/F.JPEG", "caption": "Restaurant", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/G.JPEG", "caption": "Restaurant", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/H.JPEG", "caption": "Restaurant", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/I.JPEG", "caption": "Restaurant", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/J.JPEG", "caption": "Restaurant", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/9CD1FFB7FC9542C6ABCA97329E1E9B8F/K.JPEG", "caption": "Restaurant", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/54DAAD825B0244F881F86229D0617129.JPEG", "caption": "Restaurant", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "54DAAD825B0244F881F86229D0617129", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/A.JPEG", "caption": "Restaurant", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/B.JPEG", "caption": "Restaurant", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/C.JPEG", "caption": "Restaurant", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/D.JPEG", "caption": "Restaurant", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/E.JPEG", "caption": "Restaurant", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/F.JPEG", "caption": "Restaurant", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/G.JPEG", "caption": "Restaurant", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/H.JPEG", "caption": "Restaurant", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/I.JPEG", "caption": "Restaurant", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/J.JPEG", "caption": "Restaurant", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/54DAAD825B0244F881F86229D0617129/K.JPEG", "caption": "Restaurant", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/3CE4C6DFF083468CA6F5F618425D6E33.JPEG", "caption": "Restaurant", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "3CE4C6DFF083468CA6F5F618425D6E33", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/A.JPEG", "caption": "Restaurant", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/B.JPEG", "caption": "Restaurant", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/C.JPEG", "caption": "Restaurant", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/D.JPEG", "caption": "Restaurant", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/E.JPEG", "caption": "Restaurant", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/F.JPEG", "caption": "Restaurant", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/G.JPEG", "caption": "Restaurant", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/H.JPEG", "caption": "Restaurant", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/I.JPEG", "caption": "Restaurant", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/J.JPEG", "caption": "Restaurant", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}, {"url": "https://d337bya0361y90.cloudfront.net/3CE4C6DFF083468CA6F5F618425D6E33/K.JPEG", "caption": "Restaurant", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.Descriptions"}], "Guest room": [{"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/3CF718C9416D45F1A5D9431078CF715D.JPEG", "caption": "Guest room", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "3CF718C9416D45F1A5D9431078CF715D", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/A.JPEG", "caption": "Guest room", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/B.JPEG", "caption": "Guest room", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/C.JPEG", "caption": "Guest room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/D.JPEG", "caption": "Guest room", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/E.JPEG", "caption": "Guest room", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/F.JPEG", "caption": "Guest room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/G.JPEG", "caption": "Guest room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/H.JPEG", "caption": "Guest room", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/I.JPEG", "caption": "Guest room", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/J.JPEG", "caption": "Guest room", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/3CF718C9416D45F1A5D9431078CF715D/K.JPEG", "caption": "Guest room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/E115A602F67C4AD9AD99C8DD56FBB82C.JPEG", "caption": "Guest room", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "E115A602F67C4AD9AD99C8DD56FBB82C", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/A.JPEG", "caption": "Guest room", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/B.JPEG", "caption": "Guest room", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/C.JPEG", "caption": "Guest room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/D.JPEG", "caption": "Guest room", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/E.JPEG", "caption": "Guest room", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/F.JPEG", "caption": "Guest room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/G.JPEG", "caption": "Guest room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/H.JPEG", "caption": "Guest room", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/I.JPEG", "caption": "Guest room", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/J.JPEG", "caption": "Guest room", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/E115A602F67C4AD9AD99C8DD56FBB82C/K.JPEG", "caption": "Guest room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/4B1654BEEF5A4066800E9CEB9948242B.JPEG", "caption": "Guest room", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "4B1654BEEF5A4066800E9CEB9948242B", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/A.JPEG", "caption": "Guest room", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/B.JPEG", "caption": "Guest room", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/C.JPEG", "caption": "Guest room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/D.JPEG", "caption": "Guest room", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/E.JPEG", "caption": "Guest room", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/F.JPEG", "caption": "Guest room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/G.JPEG", "caption": "Guest room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/H.JPEG", "caption": "Guest room", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/I.JPEG", "caption": "Guest room", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/J.JPEG", "caption": "Guest room", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/4B1654BEEF5A4066800E9CEB9948242B/K.JPEG", "caption": "Guest room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/F623336962CB42F39430E44B267FD868.JPEG", "caption": "Guest room", "width": "500", "height": "500", "imageFileFormat": "5", "dimensionCategory": "", "recordId": "F623336962CB42F39430E44B267FD868", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/A.JPEG", "caption": "Guest room", "width": "70", "height": "70", "imageFileFormat": "5", "dimensionCategory": "A", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/B.JPEG", "caption": "Guest room", "width": "100", "height": "100", "imageFileFormat": "5", "dimensionCategory": "B", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/C.JPEG", "caption": "Guest room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "C", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/D.JPEG", "caption": "Guest room", "width": "200", "height": "200", "imageFileFormat": "5", "dimensionCategory": "D", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/E.JPEG", "caption": "Guest room", "width": "250", "height": "250", "imageFileFormat": "5", "dimensionCategory": "E", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/F.JPEG", "caption": "Guest room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "F", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/G.JPEG", "caption": "Guest room", "width": "300", "height": "300", "imageFileFormat": "5", "dimensionCategory": "G", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/H.JPEG", "caption": "Guest room", "width": "350", "height": "350", "imageFileFormat": "5", "dimensionCategory": "H", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/I.JPEG", "caption": "Guest room", "width": "384", "height": "384", "imageFileFormat": "5", "dimensionCategory": "I", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/J.JPEG", "caption": "Guest room", "width": "480", "height": "480", "imageFileFormat": "5", "dimensionCategory": "J", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}, {"url": "https://d337bya0361y90.cloudfront.net/F623336962CB42F39430E44B267FD868/K.JPEG", "caption": "Guest room", "width": "150", "height": "150", "imageFileFormat": "5", "dimensionCategory": "K", "recordId": "", "sourcePath": "HotelInfo.CategoryCodes.GuestRoomInfo"}]}, "description": [{"text": "The Novotel Campo Naciones is a 4-star hotel with 246 rooms, located close to the IFEMA, the Municipal Congress Hall and Juan Carlos I Park. Easily accessible from the M40 highway (exit 8), the hotel has excellent subway links with the center of Madrid and the airport (55 yards [50 m] from the hotel). Ideal for seminars and conferences (9 meeting rooms), the hotel also has a restaurant, bar, outdoor pool (check opening times), free WIFI and paid parking and offers free airport transfers (check timetable).", "title": "", "language": "en"}]}, "roomTypes": [{"roomTypeCode": "S1Q", "roomTypeName": "Junior Suite/Mini Suite", "roomTypeDescription": "Junior Suite/Mini Suite with <PERSON> Si<PERSON>", "bedType": "Queen Size Bed", "numberOfBeds": 1, "lowestPrice": 556.92, "highestPrice": 714, "currencyCode": "EUR", "hasBreakfast": false, "isRefundable": true, "rooms": [{"rph": "8", "roomTypeCode": "S1Q", "roomTypeName": "Junior Suite/Mini Suite", "roomTypeDescription": "Junior Suite/Mini Suite with <PERSON> Si<PERSON>", "bedType": "Queen Size Bed", "numberOfBeds": 1, "ratePlanCode": "1KD", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "S1Q1KD", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 714, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "FLEX - RO B2C-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "formatted": true, "language": "EN"}, {"text": "hhhhhhhhhh", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "11", "roomTypeCode": "S1Q", "roomTypeName": "Junior Suite/Mini Suite", "roomTypeDescription": "Junior Suite/Mini Suite with <PERSON> Si<PERSON>", "bedType": "Queen Size Bed", "numberOfBeds": 1, "ratePlanCode": "RDI", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "S1QRDI", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 714, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "Rack rate-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "formatted": true, "language": "EN"}, {"text": "hhhhhhhhhh", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "10", "roomTypeCode": "S1Q", "roomTypeName": "Junior Suite/Mini Suite", "roomTypeDescription": "Junior Suite/Mini Suite with <PERSON> Si<PERSON>", "bedType": "Queen Size Bed", "numberOfBeds": 1, "ratePlanCode": "FGR", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "S1QFGR", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 585.48, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "FGR Itzel-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "formatted": true, "language": "EN"}, {"text": "hhhhhhhhhh", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "9", "roomTypeCode": "S1Q", "roomTypeName": "Junior Suite/Mini Suite", "roomTypeDescription": "Junior Suite/Mini Suite with <PERSON> Si<PERSON>", "bedType": "Queen Size Bed", "numberOfBeds": 1, "ratePlanCode": "1SL", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "S1Q1SL", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 556.92, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "Best Rate-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "formatted": true, "language": "EN"}, {"text": "hhhhhhhhhh", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}]}, {"roomTypeCode": "C3D", "roomTypeName": "Concierge/Executive Suite", "roomTypeDescription": "Concierge/Executive Suite with 3 Double Beds", "bedType": "Double Bed", "numberOfBeds": 3, "lowestPrice": 354.12, "highestPrice": 464, "currencyCode": "EUR", "hasBreakfast": false, "isRefundable": true, "rooms": [{"rph": "6", "roomTypeCode": "C3D", "roomTypeName": "Concierge/Executive Suite", "roomTypeDescription": "Concierge/Executive Suite with 3 Double Beds", "bedType": "Double Bed", "numberOfBeds": 3, "ratePlanCode": "RDI", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "C3DRDI", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 464, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "Rack rate-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "formatted": true, "language": "EN"}, {"text": "PPPPPPPPP", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "0", "roomTypeCode": "C3D", "roomTypeName": "Concierge/Executive Suite", "roomTypeDescription": "Concierge/Executive Suite with 3 Double Beds", "bedType": "Double Bed", "numberOfBeds": 3, "ratePlanCode": "1KD", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "C3D1KD", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 454, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "FLEX - RO B2C-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "formatted": true, "language": "EN"}, {"text": "PPPPPPPPP", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "4", "roomTypeCode": "C3D", "roomTypeName": "Concierge/Executive Suite", "roomTypeDescription": "Concierge/Executive Suite with 3 Double Beds", "bedType": "Double Bed", "numberOfBeds": 3, "ratePlanCode": "FGR", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "C3DFGR", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 372.28, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "FGR Itzel-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "formatted": true, "language": "EN"}, {"text": "PPPPPPPPP", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "2", "roomTypeCode": "C3D", "roomTypeName": "Concierge/Executive Suite", "roomTypeDescription": "Concierge/Executive Suite with 3 Double Beds", "bedType": "Double Bed", "numberOfBeds": 3, "ratePlanCode": "1SL", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "C3D1SL", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 354.12, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "Best Rate-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "formatted": true, "language": "EN"}, {"text": "PPPPPPPPP", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}]}, {"roomTypeCode": "B1Q", "roomTypeName": "Business Room", "roomTypeDescription": "Business Room with Queen Size Bed", "bedType": "Queen Size Bed", "numberOfBeds": 1, "lowestPrice": 354.12, "highestPrice": 464, "currencyCode": "EUR", "hasBreakfast": false, "isRefundable": true, "rooms": [{"rph": "7", "roomTypeCode": "B1Q", "roomTypeName": "Business Room", "roomTypeDescription": "Business Room with Queen Size Bed", "bedType": "Queen Size Bed", "numberOfBeds": 1, "ratePlanCode": "RDI", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "B1QRDI", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 464, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "Rack rate-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnJ", "formatted": true, "language": "EN"}, {"text": "JKJKJUHY", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "1", "roomTypeCode": "B1Q", "roomTypeName": "Business Room", "roomTypeDescription": "Business Room with Queen Size Bed", "bedType": "Queen Size Bed", "numberOfBeds": 1, "ratePlanCode": "1KD", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "B1Q1KD", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 454, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "FLEX - RO B2C-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnJ", "formatted": true, "language": "EN"}, {"text": "JKJKJUHY", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "5", "roomTypeCode": "B1Q", "roomTypeName": "Business Room", "roomTypeDescription": "Business Room with Queen Size Bed", "bedType": "Queen Size Bed", "numberOfBeds": 1, "ratePlanCode": "FGR", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "B1QFGR", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 372.28, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "FGR Itzel-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnJ", "formatted": true, "language": "EN"}, {"text": "JKJKJUHY", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}, {"rph": "3", "roomTypeCode": "B1Q", "roomTypeName": "Business Room", "roomTypeDescription": "Business Room with Queen Size Bed", "bedType": "Queen Size Bed", "numberOfBeds": 1, "ratePlanCode": "1SL", "ratePlanCategory": "", "rateIndicator": "AvailableForSale", "availabilityStatus": "AvailableForSale", "bookingCode": "B1Q1SL", "mealsIncluded": {"breakfast": false, "breakfastText": "Room Only", "mealPlanIndicator": "0"}, "price": {"amount": 354.12, "currencyCode": "EUR", "additionalFeesExcluded": true}, "taxes": [], "cancelPenalties": [{"cancelPolicyIndicator": "1", "policyCode": "Cancellation", "nonRefundable": false, "description": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged."}], "rateConditions": [{"text": "Best Rate-Room only", "formatted": true, "language": "EN"}, {"text": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnJ", "formatted": true, "language": "EN"}, {"text": "JKJKJUHY", "formatted": true, "language": "EN"}], "amenities": [], "paymentMethods": [{"cardCode": "AX", "cardName": "American Express"}, {"cardCode": "CA", "cardName": "MasterCard"}, {"cardCode": "DC", "cardName": "Diners Club"}, {"cardCode": "EC", "cardName": "Eurocard"}, {"cardCode": "IK", "cardName": "IKEA"}, {"cardCode": "EE", "cardName": "Visa Electron"}], "guarantee": {"guaranteeCode": "31", "guaranteeType": "GuaranteeRequired", "holdTime": "18:00:00"}, "commission": {"statusType": "Non-paying"}}]}], "checkinDate": "2025-05-30", "checkoutDate": "2025-06-01", "adults": 1, "children": [{"count": 1, "age": 6}]}