generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String    @id @default(uuid())
  email        String    @unique
  password     String
  firstName    String    @map("first_name")
  lastName     String    @map("last_name")
  birthDate    DateTime? @map("birth_date")
  gender       Int?
  referralCode String?   @map("referral_code")
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}

model Chain {
  chainCode String  @id @map("chain_code")
  chainName String  @map("chain_name")
  hotels    Hotel[] @relation("hotel_chain")

  @@map("Chain")
}

model Country {
  countryCode String  @id @map("country_code")
  countryName String  @map("country_name")
  cities      City[]  @relation("hotel_country")
  hotels      Hotel[] @relation("hotel_country")

  @@map("Country")
}

model Hotel {
  propertyCode        String   @id @map("property_code")
  city                String   @map("city")
  propertyIdentifier  String   @map("property_identifier")
  propertyName        String   @map("property_name")
  location            String   @map("location")
  transportation      String   @map("transportation")
  addressLine1        String   @map("address_line_1")
  addressLine2        String?  @map("address_line_2")
  city2               String   @map("city_2")
  stateCode           String?  @map("state_code")
  zipCode             String?  @map("zip_code")
  phone               String   @map("phone")
  fax                 String?  @map("fax")
  localRating         Int?     @map("local_rating")
  selfRating          String?  @map("self_rating")
  latitude            Float    @map("latitude")
  longitude           Float    @map("longitude")
  dupePoolId          String   @map("dupe_pool_id")
  providerValue       String?  @map("provider_value")
  lastUpdateTimestamp DateTime @map("last_update_timestamp")
  chainCode           String   @map("chain_code")
  countryCode         String   @map("country_code")
  chain               Chain    @relation("hotel_chain", fields: [chainCode], references: [chainCode])
  country             Country  @relation("hotel_country", fields: [countryCode], references: [countryCode])

  @@map("Hotel")
}


model City {
  cityCode String  @id @map("city_code")
  cityName String  @map("city_name")
  countryCode String @map("country_code")
  country Country @relation("hotel_country", fields: [countryCode], references: [countryCode])

  @@map("City")
}

model Amenities {
  amenityCode String @id @map("amenity_code") @unique
  amenityName String @map("amenity_name")
  description String @map("description")
  

  @@map("Amenities")
}

model CodeList {
  id Int @id @default(autoincrement())
  code String @map("code")
  name String @map("name")
  description String @map("description")
  type String @map("type")

  @@map("CodeList")
}
