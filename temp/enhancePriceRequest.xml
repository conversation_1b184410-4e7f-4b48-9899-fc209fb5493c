<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ses="http://xml.amadeus.com/2010/06/Session_v3">
    <soap:Header>
        <ses:Session TransactionStatusCode="InSeries">
            <ses:SessionId>00DTTG7SC0</ses:SessionId>
            <ses:SequenceNumber>3</ses:SequenceNumber>
            <ses:SecurityToken>15GNKWHJBBABC1AZLQFPNOT47X</ses:SecurityToken>
        </ses:Session>
        <add:MessageID xmlns:add="http://www.w3.org/2005/08/addressing">903ce933-c256-4cfc-b4d4-592f76b08550</add:MessageID>
        <add:Action xmlns:add="http://www.w3.org/2005/08/addressing">http://webservices.amadeus.com/Hotel_EnhancedPricing_2.0</add:Action>
        <add:To xmlns:add="http://www.w3.org/2005/08/addressing">https://noded5.test.webservices.amadeus.com/1ASIWVIELT4</add:To>
        <link:TransactionFlowLink xmlns:link="http://wsdl.amadeus.com/2010/06/ws/Link_v1" />
    </soap:Header>
    <soap:Body>
        <OTA_HotelAvailRQ EchoToken="Pricing" Version="4.000" PrimaryLangID="EN" SummaryOnly="false" AvailRatesOnly="true" OnRequestInd="true" RateDetailsInd="true" SearchCacheLevel="Live" RequestedCurrency="EUR" ExactMatchOnly="true" RateRangeOnly="false">
            <AvailRequestSegments>
                <AvailRequestSegment InfoSource="Distribution">
                    <HotelSearchCriteria>
                        <Criterion ExactMatch="true">
                            <HotelRef HotelCodeContext="1A" ChainCode="RT" HotelCityCode="MAD" HotelCode="RTMADCMP" />
                            <StayDateRange Start="2025-05-30" End="2025-06-01" />
                            <RatePlanCandidates>
                                <RatePlanCandidate RatePlanCode="1SL"></RatePlanCandidate>
                            </RatePlanCandidates>
                            <RoomStayCandidates>
                                <RoomStayCandidate RoomTypeCode="B1Q" RoomID="1" Quantity="1" BookingCode="B1Q1SL">
                                    <GuestCounts IsPerRoom="true">
  <GuestCount AgeQualifyingCode="10" Count="1"/>
  <GuestCount AgeQualifyingCode="8" Count="1" Age="6"/>
</GuestCounts>
                                </RoomStayCandidate>
                            </RoomStayCandidates>
                        </Criterion>
                    </HotelSearchCriteria>
                </AvailRequestSegment>
            </AvailRequestSegments>
        </OTA_HotelAvailRQ>
    </soap:Body>
</soap:Envelope>