{"soap:envelope": {"xmlns:soap": "http://schemas.xmlsoap.org/soap/envelope/", "xmlns:awsse": "http://xml.amadeus.com/2010/06/Session_v3", "xmlns:wsa": "http://www.w3.org/2005/08/addressing", "soap:header": {"wsa:to": "http://www.w3.org/2005/08/addressing/anonymous", "wsa:from": {"wsa:address": "https://noded5.test.webservices.amadeus.com/1ASIWVIELT4"}, "wsa:action": "http://webservices.amadeus.com/Hotel_MultiSingleAvailability_10.0", "wsa:messageid": "urn:uuid:4523f05d-560a-0ba4-3194-15818ca6c925", "wsa:relatesto": {"_": "2084792c-03be-4eae-8b60-4b6d586106eb", "RelationshipType": "http://www.w3.org/2005/08/addressing/reply"}, "awsse:session": {"TransactionStatusCode": "InSeries", "awsse:sessionid": "00DTTG7SC0", "awsse:sequencenumber": "1", "awsse:securitytoken": "15GNKWHJBBABC1AZLQFPNOT47X"}}, "soap:body": {"ota_hotelavailrs": {"xmlns": "http://www.opentravel.org/OTA/2003/05", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:schemaLocation": "http://www.opentravel.org/OTA/2003/05 OTA_HotelAvailRS.xsd", "EchoToken": "MultiSingle", "Version": "6.001", "PrimaryLangID": "EN", "success": "", "warnings": {"warning": {"Type": "3", "Status": "PRV.1", "Tag": "OK"}}, "hotelstays": {"hotelstay": {"RoomStayRPH": "0 1 2 3 4 5 6 7 8 9 10 11", "basicpropertyinfo": {"ChainCode": "RT", "HotelCode": "RTMADCMP", "HotelCityCode": "MAD", "HotelName": "NOVOTEL MADRID CAMPO NACIONES", "HotelCodeContext": "1A", "ChainName": "ACCOR HOTELS", "AreaID": "1", "HotelSegmentCategoryCode": "7", "SupplierIntegrationLevel": "3", "vendormessages": {"vendormessage": {"InfoType": "3", "subsection": {"paragraph": [{"text": {"_": "ONLINE CHECK IN AVAILABLE WITH LOYALTY CARD NO.", "Formatted": "1"}}, {"text": {"_": "CHECK ALLSAFE DETAILS ON all.accor.com/event/allsafe.en.shtml", "Formatted": "1"}}, {"Name": "Image-B.1.ID999", "url": "https://d337bya0361y90.cloudfront.net/1D7009865E5F499CB6DF2D1A035BEA86/B.JPEG"}]}}}, "position": {"Latitude": "4046248", "Longitude": "-361447"}, "address": {"addressline": "CALLE AMSTERDAM, 3", "cityname": "MADRID", "postalcode": "28042", "countryname": {"Code": "ES"}}, "contactnumbers": {"contactnumber": [{"PhoneTechType": "1", "PhoneNumber": "34/91/7211818"}, {"PhoneTechType": "3", "PhoneNumber": "34/91/7211122"}]}, "award": [{"Provider": "LSR", "Rating": "4"}, {"Provider": "MIC", "Rating": "4"}, {"Provider": "MOB", "Rating": "4"}], "relativeposition": {"transportations": {"transportation": {"TransportationCode": "17"}}}}}}, "roomstays": {"roomstay": [{"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "0", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M3D", "RoomTypeCode": "C3D"}}, "rateplans": {"rateplan": {"RatePlanCode": "1KD", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "C3D1KD", "RoomTypeCode": "C3D", "NumberOfUnits": "1", "RatePlanCode": "1KD", "RatePlanCategory": "Converted:BAR:P", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "227.00", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room C3D", "CreatorID": "1", "text": [{"_": "FLEX - RO B2C-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "Formatted": "1", "Language": "EN"}, {"_": "PPPPPPPPP", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "454.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "454.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "1", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M1Q", "RoomTypeCode": "B1Q"}}, "rateplans": {"rateplan": {"RatePlanCode": "1KD", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "B1Q1KD", "RoomTypeCode": "B1Q", "NumberOfUnits": "1", "RatePlanCode": "1KD", "RatePlanCategory": "Converted:BAR:P", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "227.00", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room B1Q", "CreatorID": "1", "text": [{"_": "FLEX - RO B2C-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnJ", "Formatted": "1", "Language": "EN"}, {"_": "JKJKJUHY", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "454.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "454.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "2", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M3D", "RoomTypeCode": "C3D"}}, "rateplans": {"rateplan": {"RatePlanCode": "1SL", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "C3D1SL", "RoomTypeCode": "C3D", "NumberOfUnits": "1", "RatePlanCode": "1SL", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "177.06", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room C3D", "CreatorID": "1", "text": [{"_": "Best Rate-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "Formatted": "1", "Language": "EN"}, {"_": "PPPPPPPPP", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "354.12", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "354.12", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "3", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M1Q", "RoomTypeCode": "B1Q"}}, "rateplans": {"rateplan": {"RatePlanCode": "1SL", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "B1Q1SL", "RoomTypeCode": "B1Q", "NumberOfUnits": "1", "RatePlanCode": "1SL", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "177.06", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room B1Q", "CreatorID": "1", "text": [{"_": "Best Rate-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnJ", "Formatted": "1", "Language": "EN"}, {"_": "JKJKJUHY", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "354.12", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "354.12", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "4", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M3D", "RoomTypeCode": "C3D"}}, "rateplans": {"rateplan": {"RatePlanCode": "FGR", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "C3DFGR", "RoomTypeCode": "C3D", "NumberOfUnits": "1", "RatePlanCode": "FGR", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "186.14", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room C3D", "CreatorID": "1", "text": [{"_": "FGR Itzel-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "Formatted": "1", "Language": "EN"}, {"_": "PPPPPPPPP", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "372.28", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "372.28", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "5", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M1Q", "RoomTypeCode": "B1Q"}}, "rateplans": {"rateplan": {"RatePlanCode": "FGR", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "B1QFGR", "RoomTypeCode": "B1Q", "NumberOfUnits": "1", "RatePlanCode": "FGR", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "186.14", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room B1Q", "CreatorID": "1", "text": [{"_": "FGR Itzel-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnJ", "Formatted": "1", "Language": "EN"}, {"_": "JKJKJUHY", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "372.28", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "372.28", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "6", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M3D", "RoomTypeCode": "C3D"}}, "rateplans": {"rateplan": {"RatePlanCode": "RDI", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "C3DRDI", "RoomTypeCode": "C3D", "NumberOfUnits": "1", "RatePlanCode": "RDI", "RatePlanCategory": "Converted:RAC:P", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "232.00", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room C3D", "CreatorID": "1", "text": [{"_": "Rack rate-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "Formatted": "1", "Language": "EN"}, {"_": "PPPPPPPPP", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "464.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "464.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "7", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M1Q", "RoomTypeCode": "B1Q"}}, "rateplans": {"rateplan": {"RatePlanCode": "RDI", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "B1QRDI", "RoomTypeCode": "B1Q", "NumberOfUnits": "1", "RatePlanCode": "RDI", "RatePlanCategory": "Converted:RAC:P", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "232.00", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room B1Q", "CreatorID": "1", "text": [{"_": "Rack rate-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnJ", "Formatted": "1", "Language": "EN"}, {"_": "JKJKJUHY", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "464.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "464.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "8", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M1Q", "RoomTypeCode": "S1Q"}}, "rateplans": {"rateplan": {"RatePlanCode": "1KD", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "S1Q1KD", "RoomTypeCode": "S1Q", "NumberOfUnits": "1", "RatePlanCode": "1KD", "RatePlanCategory": "Converted:BAR:P", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "357.00", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room S1Q", "CreatorID": "1", "text": [{"_": "FLEX - RO B2C-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "Formatted": "1", "Language": "EN"}, {"_": "hhhhhhhhhh", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "714.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "714.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "9", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M1Q", "RoomTypeCode": "S1Q"}}, "rateplans": {"rateplan": {"RatePlanCode": "1SL", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "S1Q1SL", "RoomTypeCode": "S1Q", "NumberOfUnits": "1", "RatePlanCode": "1SL", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "278.46", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room S1Q", "CreatorID": "1", "text": [{"_": "Best Rate-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "Formatted": "1", "Language": "EN"}, {"_": "hhhhhhhhhh", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "556.92", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "556.92", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "10", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M1Q", "RoomTypeCode": "S1Q"}}, "rateplans": {"rateplan": {"RatePlanCode": "FGR", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "S1QFGR", "RoomTypeCode": "S1Q", "NumberOfUnits": "1", "RatePlanCode": "FGR", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "292.74", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room S1Q", "CreatorID": "1", "text": [{"_": "FGR Itzel-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "Formatted": "1", "Language": "EN"}, {"_": "hhhhhhhhhh", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "585.48", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "585.48", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}, {"MarketCode": "Flat", "SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "AvailabilityStatus": "AvailableForSale", "InfoSource": "RT", "RPH": "11", "roomtypes": {"roomtype": {"IsConverted": "1", "RoomType": "M1Q", "RoomTypeCode": "S1Q"}}, "rateplans": {"rateplan": {"RatePlanCode": "RDI", "RateIndicator": "AvailableForSale", "AvailabilityStatus": "AvailableForSale", "guarantee": [{"GuaranteeCode": "31", "GuaranteeType": "GuaranteeRequired", "guaranteesaccepted": {"guaranteeaccepted": [{"BookingSourceAllowedInd": "1"}, {"paymentcard": {"CardCode": "AX"}}, {"paymentcard": {"CardCode": "CA"}}, {"paymentcard": {"CardCode": "DC"}}, {"paymentcard": {"CardCode": "EC"}}, {"paymentcard": {"CardCode": "IK"}}, {"paymentcard": {"CardCode": "EE"}}]}}, {"HoldTime": "18:00:00"}], "cancelpenalties": {"CancelPolicyIndicator": "1", "cancelpenalty": {"PolicyCode": "Cancellation", "deadline": {"AbsoluteDeadline": "2025-05-29T18:01:00"}, "amountpercent": {"NmbrOfNights": "1"}, "penaltydescription": {"text": {"_": "No cancellation charge applies prior to 18:00(local time) on the day of arrival. Beyond that time, the first night will be charged.", "Formatted": "1"}}}}, "commission": {"StatusType": "Non-paying"}, "mealsincluded": {"Breakfast": "0", "MealPlanIndicator": "0"}}}, "roomrates": {"roomrate": {"BookingCode": "S1QRDI", "RoomTypeCode": "S1Q", "NumberOfUnits": "1", "RatePlanCode": "RDI", "RatePlanCategory": "Converted:RAC:P", "AvailabilityStatus": "AvailableForSale", "rates": {"rate": {"EffectiveDate": "2025-05-30", "ExpireDate": "2025-06-01", "RateTimeUnit": "Day", "base": {"AmountBeforeTax": "357.00", "CurrencyCode": "EUR"}, "paymentpolicies": {"guaranteepayment": [{"PaymentCode": "31", "GuaranteeType": "GuaranteeRequired", "acceptedpayments": {"acceptedpayment": {"paymentcard": {"Remark": "Virtual Credit Card"}}}}, {"HoldTime": "18:00:00"}]}}}, "roomratedescription": {"Name": "Room S1Q", "CreatorID": "1", "text": [{"_": "Rack rate-Room only", "Formatted": "1", "Language": "EN"}, {"_": "Dfghj klm ; 123iih, ghgk, poiuy : kk<PERSON><PERSON> mlkjnn", "Formatted": "1", "Language": "EN"}, {"_": "hhhhhhhhhh", "Formatted": "1", "Language": "EN"}]}, "total": {"AmountAfterTax": "714.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}}}, "guestcounts": {"guestcount": [{"AgeQualifyingCode": "10", "Count": "1"}, {"AgeQualifyingCode": "8", "Age": "6", "Count": "1"}]}, "timespan": {"Start": "2025-05-30", "End": "2025-06-01", "startdatewindow": {"DOW": "<PERSON><PERSON>"}, "enddatewindow": {"DOW": "Sun"}}, "total": {"AmountAfterTax": "714.00", "CurrencyCode": "EUR", "AdditionalFeesExcludedIndicator": "1"}, "servicerphs": {"servicerph": {"RPH": "0"}}}]}, "services": {"service": {"ServicePricingType": "Per person per night", "ServiceRPH": "0", "ServiceInventoryCode": "3.MPT", "Inclusive": "0", "Type": "10", "ID": "-"}}}}}}