{"Envelope": {"Header": {"To": {"__prefix": "wsa", "__text": "http://www.w3.org/2005/08/addressing/anonymous"}, "From": {"Address": {"__prefix": "wsa", "__text": "https://noded5.test.webservices.amadeus.com/1ASIWVIELT4"}, "__prefix": "wsa"}, "Action": {"__prefix": "wsa", "__text": "http://webservices.amadeus.com/Hotel_MultiSingleAvailability_10.0"}, "MessageID": {"__prefix": "wsa", "__text": "urn:uuid:a97109e3-590b-8004-55ab-259459c6a382"}, "RelatesTo": {"_RelationshipType": "http://www.w3.org/2005/08/addressing/reply", "__prefix": "wsa", "__text": "urn:uuid:0ba1a8b7-b865-4da8-8299-bb30e8329078"}, "Session": {"SessionId": {"__prefix": "awsse", "__text": "00R4AH1HKA"}, "SequenceNumber": {"__prefix": "awsse", "__text": "1"}, "SecurityToken": {"__prefix": "awsse", "__text": "23GGSU9EC1TGO97WOTZKPGJ0G"}, "_TransactionStatusCode": "InSeries", "__prefix": "awsse"}, "__prefix": "soap"}, "Body": {"OTA_HotelAvailRS": {"Success": "", "Warnings": {"Warning": {"_Type": "3", "_Status": "PRV.1", "_Tag": "OK"}}, "HotelStays": {"HotelStay": {"BasicPropertyInfo": {"VendorMessages": {"VendorMessage": {"SubSection": {"Paragraph": {"URL": "https://d337bya0361y90.cloudfront.net/BA0B254203E849FF8FA8A58965BF7D1D/B.JPEG", "_Name": "Image-B.1.ID999"}}, "_InfoType": "3"}}, "Position": {"_Latitude": "4047023", "_Longitude": "-358066"}, "Address": {"AddressLine": "49 AVENIDA GENERAL", "CityName": "MADRID", "PostalCode": "28042", "CountryName": {"_Code": "ES"}}, "ContactNumbers": {"ContactNumber": [{"_PhoneTechType": "1", "_PhoneNumber": "34/91/3010999"}, {"_PhoneTechType": "3", "_PhoneNumber": "34/91/3057350"}]}, "Award": {"_Provider": "LSR", "_Rating": "2"}, "RelativePosition": {"Transportations": {"Transportation": {"_TransportationCode": "20"}}}, "_ChainCode": "RT", "_HotelCode": "RTMADIIS", "_HotelCityCode": "MAD", "_HotelName": "IBIS MADRID AEROPUERTO", "_HotelCodeContext": "1A", "_ChainName": "ACCOR HOTELS", "_AreaID": "12", "_SupplierIntegrationLevel": "3"}, "_RoomStayRPH": "0 1 2 3 4 5 6 7 8 9 10 11"}}, "RoomStays": {"RoomStay": [{"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M1D", "_RoomTypeCode": "C1D"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "RA3", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "43.33", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "MRate avail ADVANCE SAVER-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 2 adults and 1 child"}], "_Name": "Room C1D", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"Taxes": {"Tax": [{"_Type": "Inclusive", "_Code": "19", "_Amount": "10.00", "_CurrencyCode": "EUR", "_ChargeUnit": "21"}, {"_Type": "Inclusive", "_Code": "19", "_Percent": "20.00", "_ChargeUnit": "19"}]}, "_AmountAfterTax": "144.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "C1DRA3", "_RoomTypeCode": "C1D", "_NumberOfUnits": "1", "_RatePlanCode": "RA3", "_RatePlanCategory": "Converted:PRO:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "144.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "0"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M**", "_RoomTypeCode": "ROH"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "RA3", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "52.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "MRate avail ADVANCE SAVER-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 1 or 2 persons"}], "_Name": "Room ROH", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"_AmountAfterTax": "104.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "ROHRA3", "_RoomTypeCode": "ROH", "_NumberOfUnits": "1", "_RatePlanCode": "RA3", "_RatePlanCategory": "Converted:PRO:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "104.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "1"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M1D", "_RoomTypeCode": "C1D"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "D20", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "74.25", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "ADVANCE SAVER-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 2 adults and 1 child"}], "_Name": "Room C1D", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"Taxes": {"Tax": [{"_Type": "Inclusive", "_Code": "19", "_Amount": "10.00", "_CurrencyCode": "EUR", "_ChargeUnit": "21"}, {"_Type": "Inclusive", "_Code": "19", "_ChargeUnit": "19"}]}, "_AmountAfterTax": "188.50", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "C1DD20", "_RoomTypeCode": "C1D", "_NumberOfUnits": "1", "_RatePlanCode": "D20", "_RatePlanCategory": "Converted:PRO:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "188.50", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "2"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M**", "_RoomTypeCode": "ROH"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "D20", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "75.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "ADVANCE SAVER-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 1 or 2 persons"}], "_Name": "Room ROH", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"_AmountAfterTax": "150.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "ROHD20", "_RoomTypeCode": "ROH", "_NumberOfUnits": "1", "_RatePlanCode": "D20", "_RatePlanCategory": "Converted:PRO:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "150.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "3"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M1D", "_RoomTypeCode": "C1D"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "PIB", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "99.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "FLEXIBLE RATE-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 2 adults and 1 child"}], "_Name": "Room C1D", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"Taxes": {"Tax": [{"_Type": "Inclusive", "_Code": "19", "_Amount": "10.00", "_CurrencyCode": "EUR", "_ChargeUnit": "21"}, {"_Type": "Inclusive", "_Code": "19", "_ChargeUnit": "19"}]}, "_AmountAfterTax": "238.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "C1DPIB", "_RoomTypeCode": "C1D", "_NumberOfUnits": "1", "_RatePlanCode": "PIB", "_RatePlanCategory": "Converted:BAR:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "238.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "4"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M1D", "_RoomTypeCode": "C1D"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "RAC", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "99.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "FLEXIBLE RATE-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 2 adults and 1 child"}], "_Name": "Room C1D", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"Taxes": {"Tax": [{"_Type": "Inclusive", "_Code": "19", "_Amount": "10.00", "_CurrencyCode": "EUR", "_ChargeUnit": "21"}, {"_Type": "Inclusive", "_Code": "19", "_ChargeUnit": "19"}]}, "_AmountAfterTax": "238.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "C1DRAC", "_RoomTypeCode": "C1D", "_NumberOfUnits": "1", "_RatePlanCode": "RAC", "_RatePlanCategory": "Converted:BAR:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "238.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "5"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M1D", "_RoomTypeCode": "C1D"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "RDI", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "99.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "Rack rate-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 2 adults and 1 child"}], "_Name": "Room C1D", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"Taxes": {"Tax": [{"_Type": "Inclusive", "_Code": "19", "_Amount": "10.00", "_CurrencyCode": "EUR", "_ChargeUnit": "21"}, {"_Type": "Inclusive", "_Code": "19", "_ChargeUnit": "19"}]}, "_AmountAfterTax": "238.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "C1DRDI", "_RoomTypeCode": "C1D", "_NumberOfUnits": "1", "_RatePlanCode": "RDI", "_RatePlanCategory": "Converted:RAC:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "238.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "6"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M1D", "_RoomTypeCode": "C1D"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "RDP", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "99.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "Rack Rate-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 2 adults and 1 child"}], "_Name": "Room C1D", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"Taxes": {"Tax": [{"_Type": "Inclusive", "_Code": "19", "_Amount": "10.00", "_CurrencyCode": "EUR", "_ChargeUnit": "21"}, {"_Type": "Inclusive", "_Code": "19", "_ChargeUnit": "19"}]}, "_AmountAfterTax": "238.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "C1DRDP", "_RoomTypeCode": "C1D", "_NumberOfUnits": "1", "_RatePlanCode": "RDP", "_RatePlanCategory": "Converted:RAC:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "238.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "7"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M**", "_RoomTypeCode": "ROH"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "PIB", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "100.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "FLEXIBLE RATE-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 1 or 2 persons"}], "_Name": "Room ROH", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"_AmountAfterTax": "200.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "ROHPIB", "_RoomTypeCode": "ROH", "_NumberOfUnits": "1", "_RatePlanCode": "PIB", "_RatePlanCategory": "Converted:BAR:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "200.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "8"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M**", "_RoomTypeCode": "ROH"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "RAC", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "100.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "FLEXIBLE RATE-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 1 or 2 persons"}], "_Name": "Room ROH", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"_AmountAfterTax": "200.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "ROHRAC", "_RoomTypeCode": "ROH", "_NumberOfUnits": "1", "_RatePlanCode": "RAC", "_RatePlanCategory": "Converted:BAR:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "200.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "9"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M**", "_RoomTypeCode": "ROH"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "RDI", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "100.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "Rack rate-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 1 or 2 persons"}], "_Name": "Room ROH", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"_AmountAfterTax": "200.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "ROHRDI", "_RoomTypeCode": "ROH", "_NumberOfUnits": "1", "_RatePlanCode": "RDI", "_RatePlanCategory": "Converted:RAC:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "200.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "10"}, {"RoomTypes": {"RoomType": {"_IsConverted": "1", "_RoomType": "M**", "_RoomTypeCode": "ROH"}}, "RatePlans": {"RatePlan": {"Guarantee": [{"GuaranteesAccepted": {"GuaranteeAccepted": [{"PaymentCard": {"_CardCode": "AX"}}, {"PaymentCard": {"_CardCode": "CA"}}, {"PaymentCard": {"_CardCode": "DC"}}, {"PaymentCard": {"_CardCode": "EC"}}, {"PaymentCard": {"_CardCode": "IK"}}, {"PaymentCard": {"_CardCode": "EE"}}]}, "_GuaranteeCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}], "Commission": {"_StatusType": "Non-paying"}, "MealsIncluded": {"_Breakfast": "0", "_MealPlanIndicator": "0"}, "_RatePlanCode": "RDP", "_RateIndicator": "AvailableForSale", "_AvailabilityStatus": "AvailableForSale"}}, "RoomRates": {"RoomRate": {"Rates": {"Rate": {"Base": {"_AmountBeforeTax": "100.00", "_CurrencyCode": "EUR"}, "PaymentPolicies": {"GuaranteePayment": [{"AcceptedPayments": {"AcceptedPayment": {"PaymentCard": {"_Remark": "Virtual Credit Card"}}}, "_PaymentCode": "31", "_GuaranteeType": "GuaranteeRequired"}, {"_HoldTime": "19:00:00"}]}, "_EffectiveDate": "2025-05-21", "_ExpireDate": "2025-05-23", "_RateTimeUnit": "Day"}}, "RoomRateDescription": {"Text": [{"_Formatted": "1", "_Language": "EN", "__text": "Rack Rate-Room only"}, {"_Formatted": "1", "_Language": "EN", "__text": "Room for 1 or 2 persons"}], "_Name": "Room ROH", "_CreatorID": "1"}, "Features": {"Feature": [{"_RoomAmenity": "123"}, {"_RoomAmenity": "2"}, {"_RoomAmenity": "50"}]}, "Total": {"_AmountAfterTax": "200.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "_BookingCode": "ROHRDP", "_RoomTypeCode": "ROH", "_NumberOfUnits": "1", "_RatePlanCode": "RDP", "_RatePlanCategory": "Converted:RAC:P", "_AvailabilityStatus": "AvailableForSale"}}, "GuestCounts": {"GuestCount": [{"_AgeQualifyingCode": "10", "_Count": "2"}, {"_AgeQualifyingCode": "8", "_Age": "6", "_Count": "1"}]}, "TimeSpan": {"StartDateWindow": {"_DOW": "Wed"}, "EndDateWindow": {"_DOW": "<PERSON><PERSON>"}, "_Start": "2025-05-21", "_End": "2025-05-23"}, "Total": {"_AmountAfterTax": "200.00", "_CurrencyCode": "EUR", "_AdditionalFeesExcludedIndicator": "1"}, "ServiceRPHs": {"ServiceRPH": {"_RPH": "0"}}, "_MarketCode": "Flat", "_SourceOfBusiness": "Two Step.PricingOptional.PriceNotGuaranted.InvNotGuaranted", "_AvailabilityStatus": "AvailableForSale", "_InfoSource": "RT", "_RPH": "11"}]}, "Services": {"Service": {"_ServicePricingType": "Per person per night", "_ServiceRPH": "0", "_ServiceInventoryCode": "3.MPT", "_Inclusive": "0", "_Type": "10", "_ID": "-"}}, "_xmlns": "http://www.opentravel.org/OTA/2003/05", "_xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "_xsi:schemaLocation": "http://www.opentravel.org/OTA/2003/05 OTA_HotelAvailRS.xsd", "_EchoToken": "MultiSingle", "_Version": "6.001", "_PrimaryLangID": "EN"}, "__prefix": "soap"}, "_xmlns:soap": "http://schemas.xmlsoap.org/soap/envelope/", "_xmlns:awsse": "http://xml.amadeus.com/2010/06/Session_v3", "_xmlns:wsa": "http://www.w3.org/2005/08/addressing", "__prefix": "soap"}}