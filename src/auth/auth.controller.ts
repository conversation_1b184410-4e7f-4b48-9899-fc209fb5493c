import { Body, Controller, Post } from '@nestjs/common'
import { SigninResp, SigninWithPasswordDto } from './dto/auth.dto'
import { AuthService } from './auth.service'
import { ApiTags } from '@nestjs/swagger'

@ApiTags('AuthorizationEndpoint')
// @ApiExcludeController()
@Controller({ path: 'auth', version: '1' })
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('signin')
  async signIn(@Body() signInDto: SigninWithPasswordDto): Promise<SigninResp> {
    return await this.authService.signInWithPassword(signInDto)
  }
}
