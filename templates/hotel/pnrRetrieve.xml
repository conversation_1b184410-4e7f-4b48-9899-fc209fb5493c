<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:add="http://www.w3.org/2005/08/addressing" xmlns:oas="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:oasl="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:sec="http://xml.amadeus.com/2010/06/Security_v1" xmlns:awsse="http://xml.amadeus.com/2010/06/Session_v3">
    <soap:Header>
        <add:MessageID>{{messageId}}</add:MessageID>
        <add:Action>http://webservices.amadeus.com/PNRRET_21_1_1A</add:Action>
        <add:To>{{apiUrl}}</add:To>
        <oas:Security>
            <oas:UsernameToken oasl:Id="UsernameToken-3a">
                <oas:Username>{{userName}}</oas:Username>
                <oas:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">{{nonceBase64}}</oas:Nonce>
                <oas:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">{{passwordDigest}}</oas:Password>
                <oasl:Created>{{createdTimestamp}}</oasl:Created>
            </oas:UsernameToken>
        </oas:Security>
        <sec:AMA_SecurityHostedUser>
            <sec:UserID AgentDutyCode="{{agentDutyCode}}" RequestorType="U" PseudoCityCode="{{pseudoCityCode}}" POS_Type="1"/>
        </sec:AMA_SecurityHostedUser>
        <awsse:Session TransactionStatusCode="Start"/>
    </soap:Header>
    <soap:Body>
        <PNR_Retrieve xmlns="http://xml.amadeus.com/PNRRET_21_1_1A">
            <retrievalFacts>
                <retrieve>
                    <type>2</type>
                </retrieve>
                <reservationOrProfileIdentifier>
                    <reservation>
                        <controlNumber>{{pnrLocator}}</controlNumber>
                    </reservation>
                </reservationOrProfileIdentifier>
            </retrievalFacts>
        </PNR_Retrieve>
    </soap:Body>
</soap:Envelope>