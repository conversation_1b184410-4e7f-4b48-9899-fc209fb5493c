import { ApiProperty } from '@nestjs/swagger'

export class TravellerDto {
  @ApiProperty({
    description: 'Passenger surname',
    example: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
  })
  surname: string

  @ApiProperty({
    description: 'Number of passengers',
    example: '1'
  })
  quantity: string
}

export class PassengerDto {
  @ApiProperty({
    description: 'Passenger first name',
    example: 'VAN A'
  })
  firstName: string
}

export class TravellerDetailsDto {
  @ApiProperty({
    description: 'Traveller information',
    type: TravellerDto
  })
  traveller: TravellerDto

  @ApiProperty({
    description: 'Passenger information',
    type: PassengerDto
  })
  passenger: PassengerDto
}

export class HotelPropertyDto {
  @ApiProperty({
    description: 'Hotel name',
    example: 'NOVOTEL MADRID CAMPO NACIONES'
  })
  name: string

  @ApiProperty({
    description: 'Hotel code',
    example: 'CMP'
  })
  code: string

  @ApiProperty({
    description: 'Hotel provider name',
    example: 'ACCOR HOTELS'
  })
  providerName: string
}

export class TravelProductDto {
  @ApiProperty({
    description: 'Check-in date',
    example: '060625'
  })
  depDate: string

  @ApiProperty({
    description: 'Check-out date',
    example: '080625'
  })
  arrDate: string

  @ApiProperty({
    description: 'City code',
    example: 'MAD'
  })
  cityCode: string
}

export class GeneralOptionDto {
  @ApiProperty({
    description: 'Option type',
    example: 'BC'
  })
  type: string

  @ApiProperty({
    description: 'Option free text',
    type: [String],
    example: ['B1Q1SL']
  })
  freeText: string[]
}

export class HotelBookingDetailsDto {
  @ApiProperty({
    description: 'Hotel property information',
    type: HotelPropertyDto
  })
  hotelProperty: HotelPropertyDto

  @ApiProperty({
    description: 'Travel product information',
    type: TravelProductDto
  })
  travelProduct: TravelProductDto

  @ApiProperty({
    description: 'General options',
    type: [GeneralOptionDto]
  })
  generalOptions: GeneralOptionDto[]
}

export class HotelReferenceDto {
  @ApiProperty({
    description: 'Hotel chain code',
    example: 'RT'
  })
  chainCode: string

  @ApiProperty({
    description: 'City code',
    example: 'MAD'
  })
  cityCode: string

  @ApiProperty({
    description: 'Hotel code',
    example: 'CMP'
  })
  hotelCode: string
}

export class ReservationHotelPropertyDto {
  @ApiProperty({
    description: 'Hotel name',
    example: 'NOVOTEL MADRID CAMPO NACIONES'
  })
  hotelName: string

  @ApiProperty({
    description: 'Hotel reference',
    type: HotelReferenceDto
  })
  hotelReference: HotelReferenceDto
}

export class DateDto {
  @ApiProperty({
    description: 'Year',
    example: '2025'
  })
  year: string

  @ApiProperty({
    description: 'Month',
    example: '6'
  })
  month: string

  @ApiProperty({
    description: 'Day',
    example: '6'
  })
  day: string
}

export class DatesDto {
  @ApiProperty({
    description: 'Check-in date',
    type: DateDto
  })
  checkIn: DateDto

  @ApiProperty({
    description: 'Check-out date',
    type: DateDto
  })
  checkOut: DateDto
}

export class ConfirmationDto {
  @ApiProperty({
    description: 'Company ID',
    example: '1A'
  })
  companyId: string

  @ApiProperty({
    description: 'Control number',
    example: '1636ZF5502'
  })
  controlNumber: string

  @ApiProperty({
    description: 'Control type',
    example: '2'
  })
  controlType: string
}

export class ReservationDetailsDto {
  @ApiProperty({
    description: 'Hotel property information',
    type: ReservationHotelPropertyDto
  })
  hotelProperty: ReservationHotelPropertyDto

  @ApiProperty({
    description: 'Booking dates',
    type: DatesDto
  })
  dates: DatesDto

  @ApiProperty({
    description: 'Confirmation details',
    type: ConfirmationDto
  })
  confirmation: ConfirmationDto
}

export class ContactInformationDto {
  @ApiProperty({
    description: 'Phone number',
    example: '+84912345678'
  })
  phoneNumber: string

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  email: string

  @ApiProperty({
    description: 'Special requests',
    type: [String],
    example: ['HIGH FLOOR REQUESTED']
  })
  specialRequests: string[]
}

export class PnrRetrieveDetailsDto {
  @ApiProperty({
    description: 'PNR information',
    type: PnrInfoDto
  })
  pnrInfo: PnrInfoDto

  @ApiProperty({
    description: 'Traveller details',
    type: TravellerDetailsDto
  })
  travellerDetails: TravellerDetailsDto

  @ApiProperty({
    description: 'Hotel booking details',
    type: HotelBookingDetailsDto
  })
  hotelBookingDetails: HotelBookingDetailsDto

  @ApiProperty({
    description: 'Reservation details',
    type: ReservationDetailsDto
  })
  reservationDetails: ReservationDetailsDto

  @ApiProperty({
    description: 'Contact information',
    type: ContactInformationDto
  })
  contactInformation: ContactInformationDto

  @ApiProperty({
    description: 'PNR status',
    example: 'RETRIEVED'
  })
  status: string

  @ApiProperty({
    description: 'Success message',
    example: 'PNR has been successfully retrieved'
  })
  message: string
}

export class PnrRetrieveResponseDto {
  @ApiProperty({
    description: 'Session ID',
    example: '05QVESLJQR'
  })
  sessionId: string

  @ApiProperty({
    description: 'Sequence number',
    example: '1'
  })
  sequenceNumber: string

  @ApiProperty({
    description: 'Security token',
    example: '2BIQ50LLI85G53QLYVC87L1QD6'
  })
  securityToken: string

  @ApiProperty({
    description: 'PNR details',
    type: PnrRetrieveDetailsDto
  })
  pnrDetails: PnrRetrieveDetailsDto
}
