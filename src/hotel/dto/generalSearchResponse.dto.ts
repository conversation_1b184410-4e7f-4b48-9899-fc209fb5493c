import { ApiProperty } from '@nestjs/swagger'

export class RoomDto {
  @ApiProperty({
    description: 'The room rph',
    example: 'Room RPH'
  })
  rph: string

  @ApiProperty({
    description: 'The room type',
    example: 'Room Type',
    required: false
  })
  roomType?: string

  @ApiProperty({
    description: 'The room name',
    example: 'Room Type Code',
    required: false
  })
  roomTypeCode?: string

  @ApiProperty({
    description: 'The rate plan code',
    example: 'Rate Plan Code',
    required: false
  })
  ratePlanCode?: string

  @ApiProperty({
    description: 'The rate plan code',
    example: 'Rate Plan Code',
    required: false
  })
  rateIndicator?: string

  @ApiProperty({
    description: 'The availability status',
    example: 'Availability Status'
  })
  availabilityStatus: string

  @ApiProperty({
    description: 'The availability status',
    example: 'Meals Included'
  })
  mealsIncluded: number

  @ApiProperty({
    description: 'The meal plan codes',
    example: 'Meal Plan Codes'
  })
  mealPlanCodes: string

  @ApiProperty({
    description: 'The price',
    example: 'Price'
  })
  price: string
}

export class GeneralSearchResponseDto {
  @ApiProperty({
    description: 'The chain code of the hotel',
    example: 'TUL'
  })
  chainCode: string

  @ApiProperty({
    description: 'The code of the hotel',
    example: 'TUL1S2400'
  })
  hotelCode: string

  @ApiProperty({
    description: 'The city code of the hotel',
    example: 'TUL'
  })
  hotelCityCode: string

  @ApiProperty({
    description: 'The hotel name',
    example: 'Hotel Name'
  })
  hotelName: string

  @ApiProperty({
    description: 'The hotel address',
    example: 'Hotel Address'
  })
  hotelCodeContext: string

  @ApiProperty({
    description: 'The hotel address',
    example: 'Hotel Address'
  })
  address: string

  @ApiProperty({
    description: 'The hotel location',
    example: 'Hotel Location'
  })
  location: string

  @ApiProperty({
    description: 'The hotel contact number',
    example: 'Hotel Contact Number'
  })
  contactNumber: string

  @ApiProperty({
    description: 'The hotel rate',
    example: 'Hotel Rate'
  })
  rate: string

  @ApiProperty({
    description: 'The hotel price',
    example: 'Hotel Price'
  })
  price: string

  @ApiProperty({
    description: 'The hotel breakfast',
    example: 'Hotel Breakfast'
  })
  breakfast: string

  @ApiProperty({
    description: 'The hotel rooms',
    example: 'Hotel Rooms',
    type: [RoomDto]
  })
  rooms: RoomDto[]
}
