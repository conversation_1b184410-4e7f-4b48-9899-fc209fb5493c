import { Injectable, UnauthorizedException } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { ExtractJwt, Strategy } from 'passport-jwt'
import { JWT_SECRET } from 'src/app.settings'

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: JWT_SECRET
    })
  }

  async validate(payload: any): Promise<any> {
    const { email, id, roles, scope, deviceId } = payload
    if (!id || !scope || scope === 'email-verification') {
      throw new UnauthorizedException()
    }
    return {
      id,
      email,
      roles,
      scope,
      deviceId
    }
  }
}
