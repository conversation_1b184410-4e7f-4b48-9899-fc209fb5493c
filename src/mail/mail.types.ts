export interface PaymentMail {
  guestName: string
  status: string
  time: string
  totalAmount: string
  txHash: string
  walletAddress: string
  email: string
  productName: string
  referralCode?: string
  referralType?: string
  explorerTxHash?: string
}

export interface VerifyMail {
  name: string
  code: string
  email: string
}

export interface ForgetPasswordMail {
  username: string
  password: string
  email: string
}

export enum MailType {
  CONFIRM = 'confirm',
  VERIFY = 'verify',
  FORGET_PASSWORD = 'password',
  INVITE = 'invite'
}

export enum MailLanguage {
  EN = 'en'
}

export interface InviteMailData {
  email: string
  password: string
}
