import { ApiProperty } from '@nestjs/swagger'

class ChildrenCount {
  @ApiProperty({
    description: 'The guest count count of the hotel',
    example: 0
  })
  count: number

  @ApiProperty({
    description: 'The guest count age of the hotel',
    example: 0
  })
  age: number
}

export class GeneralSearchDto {
  @ApiProperty({
    description: 'The city code of the hotel',
    example: 'MAD'
  })
  hotelCityCode: string

  @ApiProperty({
    description: 'The checkin date of the hotel',
    example: '2025-05-30'
  })
  checkinDate: string

  @ApiProperty({
    description: 'The checkout date of the hotel',
    example: '2025-06-01'
  })
  checkoutDate: string

  @ApiProperty({
    description: 'The room quantity of the hotel',
    example: 1
  })
  roomQuantity: number

  @ApiProperty({
    description: 'The guest count age qualifying code of the hotel',
    example: '10'
  })
  guestCountAgeQualifyingCode: string

  @ApiProperty({
    description: 'The guest count count of the hotel',
    example: 1
  })
  guestCount: number

  @ApiProperty({
    description: 'The guest count count of the hotel',
    example: 1
  })
  adults: number

  @ApiProperty({
    description: 'The hotel code of the hotel',
    example: 'RTMADCMP',
    required: false
  })
  hotelCode?: string

  @ApiProperty({
    description: 'The guest count count of the hotel',
    example: [
      { count: 1, age: 6 },
    ],
    type: [ChildrenCount]
  })
  childrens: ChildrenCount[]
}
