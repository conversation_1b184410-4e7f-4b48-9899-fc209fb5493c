import { Controller, Post, Body, Query, Get, HttpCode, HttpStatus } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBody } from '@nestjs/swagger'
import { HotelService } from './hotel.service'
import { GeneralSearchDto } from './dto/generalSearch.dto'
import { RoomPriceDto } from './dto/roomPrice.dto'
import { UserFillFormDto } from './dto/userFillForm.dto'
import { HotelSellDto } from './dto/hotellSell.dto'
import { DescriptiveRequestDto } from './dto/descriptiveRequest.dto'

@Controller('hotel')
export class HotelController {
  constructor(private readonly hotelService: HotelService) {}

  @Get('search')
  async search(@Query('keyword') keyword: string) {
    return await this.hotelService.search(keyword)
  }

  @Post('general-search')
  async generalSearch(@Body() data: GeneralSearchDto) {
    return await this.hotelService.gdsGeneralSearch(data)
  }

  @Post('room-search')
  async roomSearch(@Body() data: GeneralSearchDto) {
    return await this.hotelService.gdsRoomSearch(data)
  }

  @Post('descriptive-info')
  async descriptiveInfo(@Query('hotelCode') hotelCode: string) {
    return await this.hotelService.gdsDescriptiveInfo([hotelCode])
  }

  @Post('room-price-detail')
  async roomPriceDetail(@Body() data: RoomPriceDto) {
    return await this.hotelService.gdsRoomPriceDetail(data)
  }

  @Post('fill-user-info')
  async fillUserInfo(@Body() data: UserFillFormDto) {
    return await this.hotelService.gdsUserFillForm(data)
  }
  @Post('hotel-sell')
  async hotelSell(@Body() data: HotelSellDto) {
    return await this.hotelService.gdsSell(data)
  }
  
}
