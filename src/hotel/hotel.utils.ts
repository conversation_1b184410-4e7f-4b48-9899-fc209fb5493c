import { create } from 'xmlbuilder2'
import { AgeQualifyingCode } from './hotel.types'
import axios from 'axios'
import { AMADEUS_API_URL, AMADEUS_PASSWORD } from '../app.settings'
import { join } from 'path'
import { readFileSync } from 'fs'
import { v4 as uuidv4 } from 'uuid'
import * as CryptoJS from 'crypto-js'
import { parseStringPromise } from 'xml2js'

export function getTemplate(name: string) {
  return readFileSync(join(__dirname, '../../..', 'templates', 'hotel', `${name}.xml`), 'utf8')
}

export function generateGuestCountDetails(
  adults: { count: number; age: number },
  childrens: { count: number; age: number }[]
): string {
  const xml = create()
  const root = xml.ele('GuestCounts', { IsPerRoom: 'true' })

  root.ele('GuestCount', {
    AgeQualifyingCode: AgeQualifyingCode.ADULT,
    Count: adults.count
  })
  if (childrens.length > 0) {
    childrens.forEach((child) => {
      root.ele('GuestCount', {
        AgeQualifyingCode: AgeQualifyingCode.CHILD,
        Count: child.count,
        Age: child.age
      })
    })
  }

  return root.toString({ prettyPrint: true })
}

export async function postRequest(actionUrl: string, payload: string) {
  try {
    const response = await axios.post(AMADEUS_API_URL, payload, {
      headers: {
        'Content-Type': 'text/xml; charset=utf-8',
        SOAPAction: actionUrl
      }
    })
    // convert xml to json
    const json = await parseStringPromise(response.data, {
      trim: true,
      explicitArray: false,
      mergeAttrs: true,
      normalizeTags: true
    })

    return json
  } catch (error) {
    console.log({ error })
    throw error
  }
}

export function generateWSSecurityCredentials(): {
  messageId: string
  timestamp: string
  nonceBase64: string
  passwordDigest: string
} {
  // 1. Generate Message ID
  const messageId: string = uuidv4()

  // 2. Generate Timestamp
  const now: Date = new Date()
  const timestamp: string = now.toISOString().split('.')[0] + '.231Z'

  // 3. Generate Nonce
  const rawNonce: CryptoJS.lib.WordArray = CryptoJS.lib.WordArray.random(16)
  const nonceBase64: string = CryptoJS.enc.Base64.stringify(rawNonce)

  // 4. Calculate Password Digest
  const clearPassword: string = AMADEUS_PASSWORD || ''
  if (!clearPassword) {
    throw new Error('Missing clearPassword')
  }
  const passwordSha1: CryptoJS.lib.WordArray = CryptoJS.SHA1(clearPassword)
  const nonceAndTimestamp: CryptoJS.lib.WordArray = rawNonce
    .clone()
    .concat(CryptoJS.enc.Utf8.parse(timestamp))
  const combinedSha1: CryptoJS.lib.WordArray = CryptoJS.SHA1(nonceAndTimestamp.concat(passwordSha1))
  const passwordDigest: string = CryptoJS.enc.Base64.stringify(combinedSha1)

  return { messageId, timestamp, nonceBase64, passwordDigest }
}

export function generateHotelDescriptiveInfos(hotelCodes: string[]) {
  const element = hotelCodes.map((hotelCode) => {
    return `<HotelDescriptiveInfo HotelCode="${hotelCode}">
                    <HotelInfo SendData="true" />
                    <FacilityInfo SendGuestRooms="true" SendMeetingRooms="true" SendRestaurants="true" />
                    <Policies SendPolicies="true" />
                    <AreaInfo SendAttractions="true" SendRefPoints="true" SendRecreations="true" />
                    <AffiliationInfo SendAwards="true" SendLoyalPrograms="false" />
                    <ContactInfo SendData="true" />
                    <MultimediaObjects SendData="true" />
                    <ContentInfos>
                        <ContentInfo Name="SecureMultimediaURLs" />
                    </ContentInfos>
                </HotelDescriptiveInfo>`
  })
  return element.join('')
}

/**
 * Đảm bảo dữ liệu trả về là một mảng
 * @param data Dữ liệu đầu vào (có thể là mảng, đối tượng đơn lẻ hoặc undefined)
 * @returns Mảng dữ liệu
 */
export function ensureArray<T>(data: T | T[] | undefined): T[] {
  if (!data) return [];
  return Array.isArray(data) ? data : [data];
}
