import { ApiProperty } from '@nestjs/swagger'

export class RoomPriceDto {
  @ApiProperty({
    description: 'The session id from previous search',
    example: '02JV3Q93R0'
  })
  sessionId: string

  @ApiProperty({
    description: 'The sequence number from previous search',
    example: '3'
  })
  sequenceNumber: string

  @ApiProperty({
    description: 'The security token from previous search',
    example: '1YVE4IWPFJ8NQ3MECH1J0L8KH0'
  })
  securityToken: string

  @ApiProperty({
    description: 'The chain code of the hotel',
    example: 'RT'
  })
  chainCode: string

  @ApiProperty({
    description: 'The hotel city code',
    example: 'MAD'
  })
  hotelCityCode: string

  @ApiProperty({
    description: 'The hotel code',
    example: 'RTMADCMP'
  })
  hotelCode: string

  @ApiProperty({
    description: 'The checkin date',
    example: '2025-05-30'
  })
  checkinDate: string

  @ApiProperty({
    description: 'The checkout date',
    example: '2025-06-01'
  })
  checkoutDate: string

  @ApiProperty({
    description: 'The rate plan code',
    example: '1SL'
  })
  ratePlanCode: string

  @ApiProperty({
    description: 'The room type code',
    example: 'B1Q'
  })
  roomTypeCode: string

  @ApiProperty({
    description: 'The room quantity',
    example: 1
  })
  roomQuantity: number

  @ApiProperty({
    description: 'The booking code',
    example: 'B1Q1SL'
  })
  bookingCode: string

  @ApiProperty({
    description: 'The number of adults',
    example: 1
  })
  adults: number

  @ApiProperty({
    description: 'The children details',
    example: [{ count: 1, age: 6 }],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        count: { type: 'number', example: 1 },
        age: { type: 'number', example: 6 }
      }
    },
    required: false
  })
  childrens?: Array<{ count: number; age: number }>
}
