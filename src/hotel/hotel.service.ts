import { HttpException, Injectable } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { GeneralSearchDto } from './dto/generalSearch.dto'
import {
  AMADEUS_ACTION_BASE_URL,
  AMADEUS_API_URL,
  AMADEUS_DUTY_CODE,
  AMADEUS_OFFICE_ID,
  AMADEUS_PASSWORD,
  AMADEUS_USER_ID
} from '../app.settings'

import {
  generateGuestCountDetails,
  generateHotelDescriptiveInfos,
  generateWSSecurityCredentials,
  getTemplate,
  postRequest,
  ensureArray
} from './hotel.utils'
import { randomUUID, createHash, randomBytes } from 'crypto'
import { parseOtaHotelAvailRS } from './helper/searchResponse'
import { GeneralSearchResponseDto, RoomDto } from './dto/generalSearchResponse.dto'
import { writeFileSync, readFileSync } from 'fs'
import { CityResponseDto } from './dto/cityResponse.dto'
import { RoomSearchDto } from './dto/roomSearchResponse.dto'
import { DescriptiveRequestDto } from './dto/descriptiveRequest.dto'
import { parseRoomTypeCode, getPaymentCardName, ImageCategories } from './hotel.types'
import { RoomPriceDto } from './dto/roomPrice.dto'
import { UserFillFormDto } from './dto/userFillForm.dto'
import { HotelSellDto } from './dto/hotellSell.dto'

@Injectable()
export class HotelService {
  constructor(private readonly prisma: PrismaService) {}

  // search with keyword, can be city name or country name
  async search(keyword: string) {
    const cities = await this.prisma.hotel.findMany({
      where: {
        OR: [
          { city2: { contains: keyword, mode: 'insensitive' } },
          { country: { countryName: { contains: keyword, mode: 'insensitive' } } },
          { propertyName: { contains: keyword, mode: 'insensitive' } }
        ]
      },
      select: {
        propertyName: true,
        propertyCode: true,
        city2: true,
        city: true,
        country: true,
        addressLine1: true,
        addressLine2: true,
        latitude: true,
        longitude: true
      },
      take: 10
    })

    return cities
  }

  async gdsGeneralSearch(data: GeneralSearchDto) {
    // read

    //let response = JSON.parse(readFileSync('response.json', 'utf8'))
    //if (!response || Object.keys(response).length === 0) {
    const actionUrl = `${AMADEUS_ACTION_BASE_URL}/Hotel_MultiSingleAvailability_10.0`
    const {
      hotelCityCode,
      checkinDate,
      checkoutDate,
      roomQuantity,
      guestCount,
      adults,
      childrens
    } = data
    const guestCountDetailsString = generateGuestCountDetails({ count: adults, age: 0 }, childrens)
    const template = getTemplate('generalSearch')
    const { messageId, timestamp, nonceBase64, passwordDigest } = generateWSSecurityCredentials()
    const payload = template
      .replace('{{messageId}}', messageId)
      .replace('{{ActionUrlHotelMultiSingleAvailability}}', actionUrl)
      .replace('{{apiUrl}}', AMADEUS_API_URL)
      .replace('{{userName}}', AMADEUS_USER_ID)
      .replace('{{nonceBase64}}', nonceBase64)
      .replace('{{passwordDigest}}', passwordDigest)
      .replace('{{agentDutyCode}}', AMADEUS_DUTY_CODE)
      .replace('{{pseudoCityCode}}', AMADEUS_OFFICE_ID)
      .replace('{{createdTimestamp}}', timestamp)
      // body
      .replace('{{password}}', AMADEUS_PASSWORD)
      .replace('{{hotelCityCode}}', hotelCityCode)
      .replace('{{checkinDate}}', checkinDate)
      .replace('{{checkoutDate}}', checkoutDate)
      .replace('{{roomQuantity}}', roomQuantity.toString())
      .replace('{{guestCount}}', guestCount.toString())
      .replace('{{guestCountDetails}}', guestCountDetailsString)

    const response = await postRequest(actionUrl, payload)

    // write response to file
    //writeFileSync('response.json', JSON.stringify(response))
    //}
    // get some main data from otaData
    const otaData = response['soap:envelope']['soap:body'].ota_hotelavailrs
    const headers = response['soap:envelope']['soap:header']

    if (otaData.errors && otaData.errors.error) {
      throw new HttpException(otaData.errors.error.ShortText, 404)
    }

    const sessionId = headers['awsse:session']['awsse:sessionid']
    const sequenceNumber = headers['awsse:session']['awsse:sequencenumber']
    const securityToken = headers['awsse:session']['awsse:securitytoken']

    let hotelStays = otaData.hotelstays?.hotelstay

    hotelStays = Array.isArray(hotelStays) ? hotelStays : [hotelStays]
    let roomStays = otaData.roomstays?.roomstay
    roomStays = Array.isArray(roomStays) ? roomStays : [roomStays]

    hotelStays = hotelStays.filter((hotel: any) => hotel !== undefined)

    if (!hotelStays || hotelStays.length === 0) {
      return {
        sessionId,
        sequenceNumber,
        securityToken,
        hotels: []
      }
    }

    const hotels = await Promise.all(
      hotelStays.map(async (hotel: any) => {
        const roomRPHs = hotel.RoomStayRPH.split(' ')
        const roomRaws = roomStays.filter((room: any) => roomRPHs.includes(room.RPH))
        
        const rooms = roomRaws.map((room: any) => {
          let ratePlans = Array.isArray(room.rateplans?.rateplan)
            ? room.rateplans?.rateplan
            : (room.rateplans?.rateplan ? [room.rateplans.rateplan] : []); // Xử lý trường hợp rateplan có thể không tồn tại
          ratePlans = ratePlans.filter((f: any) => f !== undefined);


          const currentRoomRate = room.roomrates?.roomrate; // RoomRate của phòng hiện tại
          const currentRatePlan = ratePlans?.[0];

          // promotion
          let promotionDetails = {
            isPromotion: false,
            type: '',
            description: '',
            price: null as { amount: string; currencyCode: string } | null, // Thêm kiểu cho price
          };
          if (currentRoomRate) {
            const promotionCode = currentRoomRate.PromotionCode;
            const ratePlanCategory = currentRoomRate.RatePlanCategory;
            const ratePlanName = currentRatePlan?.RatePlanName;
            const roomTotal = currentRoomRate.total; // Lấy total từ currentRoomRate

            if (promotionCode) {
              promotionDetails.isPromotion = true;
              promotionDetails.type = promotionCode;
              promotionDetails.description = `Promotion code: ${promotionCode}`;
              if (roomTotal) {
                promotionDetails.price = {
                  amount: roomTotal.AmountAfterTax || roomTotal.AmountBeforeTax || '',
                  currencyCode: roomTotal.CurrencyCode || '',
                };
              }
            } else if (ratePlanCategory && typeof ratePlanCategory === 'string' && ratePlanCategory.toUpperCase().includes(':PRO:')) {
              promotionDetails.isPromotion = true;
              promotionDetails.type = 'PRO';
              promotionDetails.description = ratePlanName || 'Promotional Rate';
              if (roomTotal) {
                promotionDetails.price = {
                  amount: roomTotal.AmountAfterTax || roomTotal.AmountBeforeTax || '',
                  currencyCode: roomTotal.CurrencyCode || '',
                };
              }
            } else if (ratePlanName && typeof ratePlanName === 'string' &&
                       (ratePlanName.toUpperCase().includes('PROMO') ||
                        ratePlanName.toUpperCase().includes('SALE') ||
                        ratePlanName.toUpperCase().includes('OFFER') ||
                        ratePlanName.toUpperCase().includes('DEAL'))) {
              promotionDetails.isPromotion = true;
              promotionDetails.type = 'TEXT_BASED_PROMO';
              promotionDetails.description = ratePlanName;
              if (roomTotal) {
                promotionDetails.price = {
                  amount: roomTotal.AmountAfterTax || roomTotal.AmountBeforeTax || '',
                  currencyCode: roomTotal.CurrencyCode || '',
                };
              }
            }
          }
          // const roomRates = room.RoomRates.RoomRate
          // const serviceRPHs = room.ServiceRPHs.ServiceRPH
          const roomData = {
            rph: room.RPH,
            roomType: room.roomtypes, // Nên parse chi tiết hơn nếu cần
            roomTypeCode: currentRoomRate?.RoomTypeCode || room.roomtypes?.roomtype?.RoomTypeCode || '', // Ưu tiên từ RoomRate
            ratePlanCode: currentRatePlan?.RatePlanCode || '',
            rateIndicator: currentRatePlan?.RateIndicator || '',
            availabilityStatus: room.AvailabilityStatus || currentRoomRate?.AvailabilityStatus || '', // Ưu tiên từ Room
            mealsIncluded: currentRatePlan?.mealsincluded?.Breakfast === '1' || currentRatePlan?.mealsincluded?.Breakfast === true || '', // Chuyển thành boolean hoặc giữ string
            mealPlanCodes: currentRatePlan?.mealsincluded?.MealPlanCodes || '',
            price: room.total?.AmountAfterTax || currentRoomRate?.total?.AmountAfterTax || '', // Ưu tiên từ Room
            currencyCode: room.total?.CurrencyCode || currentRoomRate?.total?.CurrencyCode || '', // Thêm currency code
            promotion: promotionDetails, // Thêm thông tin khuyến mãi vào đây
            // Các trường khác từ RoomRate nếu cần (ví dụ: bookingCode)
            bookingCode: currentRoomRate?.BookingCode || '',
            // ... các chi tiết giá từ currentRoomRate.rates ...
          };
          return roomData;
        });

        //console.log(rooms)
        const lowestPrice = rooms.reduce((min: number, room: RoomDto) => {
          return Math.min(min, Number(room.price))
        }, Number(rooms[0].price))

        const breakfast =
          rooms.find((room: RoomDto) => Number(room.mealsIncluded) > 0)?.mealsIncluded || 0

        const hotelData = {
          hotelCode: hotel.basicpropertyinfo?.HotelCode || '',
          hotelName: hotel.basicpropertyinfo?.HotelName || '',
          hotelCityCode: hotel.basicpropertyinfo?.HotelCityCode || '',
          chainCode: hotel.basicpropertyinfo?.ChainCode || '',
          hotelCodeContext: hotel.basicpropertyinfo?.HotelCodeContext || '',
          address: hotel.basicpropertyinfo?.address || '',
          location: hotel.basicpropertyinfo?.position || '',
          contactNumber: hotel.basicpropertyinfo?.contactnumbers?.contactnumber || '',
          rate: hotel.basicpropertyinfo?.award || '',
          price: lowestPrice.toString(),
          breakfast,
          rooms: rooms
        }
        return hotelData
      })
    )

    const filteredHotels = hotels
      .sort((a: any, b: any) => Number(a.price) - Number(b.price))
      .filter(
        (hotel: any, index: number, self: any[]) =>
          index === self.findIndex((h: any) => h.hotelCode === hotel.hotelCode)
      )

    // get some hotel amenities + images
    const hotelDescriptiveInfos = await this.gdsDescriptiveInfo(
      filteredHotels.map((hotel: any) => hotel.hotelCode)
    )

    // diff between hotels and filteredHotels
    const diffHotels = hotels.filter((hotel: any) => !filteredHotels.includes(hotel))
    console.log({ diffHotelsLength: diffHotels.length, diffHotels })

    return {
      sessionId,
      sequenceNumber,
      securityToken,
      hotels: hotels.map((hotel: any) => ({
        ...filteredHotels,
        amenities: hotelDescriptiveInfos.find((info: any) => info.hotelCode === hotel.hotelCode)
      }))
    }
  }

  async gdsRoomSearch(data: GeneralSearchDto) {
    const actionUrl = `${AMADEUS_ACTION_BASE_URL}/Hotel_MultiSingleAvailability_10.0`
    const {
      hotelCityCode,
      checkinDate,
      checkoutDate,
      roomQuantity,
      guestCount,
      adults,
      childrens,
      hotelCode
    } = data
    const guestCountDetailsString = generateGuestCountDetails({ count: adults, age: 0 }, childrens)
    const template = getTemplate('roomSearch')
    const { messageId, timestamp, nonceBase64, passwordDigest } = generateWSSecurityCredentials()
    const payload = template
      .replace('{{messageId}}', messageId)
      .replace('{{ActionUrlHotelMultiSingleAvailability}}', actionUrl)
      .replace('{{apiUrl}}', AMADEUS_API_URL)
      .replace('{{userName}}', AMADEUS_USER_ID)
      .replace('{{nonceBase64}}', nonceBase64)
      .replace('{{passwordDigest}}', passwordDigest)
      .replace('{{agentDutyCode}}', AMADEUS_DUTY_CODE)
      .replace('{{pseudoCityCode}}', AMADEUS_OFFICE_ID)
      .replace('{{createdTimestamp}}', timestamp)
      // body
      .replace('{{password}}', AMADEUS_PASSWORD)
      .replace('{{hotelCityCode}}', hotelCityCode)
      .replace('{{checkinDate}}', checkinDate)
      .replace('{{checkoutDate}}', checkoutDate)
      .replace('{{roomQuantity}}', roomQuantity.toString())
      .replace('{{guestCount}}', guestCount.toString())
      .replace('{{guestCountDetails}}', guestCountDetailsString)
      .replace('{{hotelCode}}', hotelCode || '')

    // console.log('=== ROOM SEARCH REQUEST TEMPLATE ===');
    // console.log(payload);
    // console.log('=== END ROOM SEARCH REQUEST TEMPLATE ===');

    writeFileSync('./temp/roomSearchRequest.xml', payload)

    const response = await postRequest(actionUrl, payload)

    // Write response to file for debugging
    writeFileSync('./temp/roomSearchResponse.json', JSON.stringify(response, null, 2))

    // Process response
    const otaData = response['soap:envelope']['soap:body'].ota_hotelavailrs
    const headers = response['soap:envelope']['soap:header']

    if (otaData.errors && otaData.errors.error) {
      throw new HttpException(otaData.errors.error.ShortText, 404)
    }

    const sessionId = headers['awsse:session']['awsse:sessionid']
    const sequenceNumber = headers['awsse:session']['awsse:sequencenumber']
    const securityToken = headers['awsse:session']['awsse:securitytoken']

    let hotelStays = otaData.hotelstays?.hotelstay
    if (!hotelStays) {
      return {
        sessionId,
        sequenceNumber,
        securityToken,
        roomTypes: []
      }
    }

    hotelStays = Array.isArray(hotelStays) ? hotelStays : [hotelStays]
    let roomStays = otaData.roomstays?.roomstay
    roomStays = Array.isArray(roomStays) ? roomStays : [roomStays]

    const hotel = hotelStays[0]
    const hotelInfo = {
      hotelCode: hotel.basicpropertyinfo.HotelCode,
      hotelName: hotel.basicpropertyinfo.HotelName,
      hotelCityCode: hotel.basicpropertyinfo.HotelCityCode,
      chainCode: hotel.basicpropertyinfo.ChainCode,
      chainName: hotel.basicpropertyinfo.ChainName,
      hotelCodeContext: hotel.basicpropertyinfo.HotelCodeContext || '',
      address: hotel.basicpropertyinfo.address || '',
      location: hotel.basicpropertyinfo.position || '',
      contactNumber: hotel.basicpropertyinfo.contactnumbers?.contactnumber || '',
      rating: hotel.basicpropertyinfo.award[0]?.Rating || ''
    }

    const roomRPHs = hotel.RoomStayRPH.split(' ')
    const roomRaws = roomStays.filter((room: any) => roomRPHs.includes(room.RPH))

    const processedRooms = roomRaws.map((room: any) => {
      let ratePlans = Array.isArray(room.rateplans?.rateplan)
        ? room.rateplans?.rateplan
        : [room.rateplans?.rateplan]
      ratePlans = ratePlans.filter((f: any) => f !== undefined)

      let roomRates = Array.isArray(room.roomrates?.roomrate)
        ? room.roomrates?.roomrate
        : [room.roomrates?.roomrate]
      roomRates = roomRates.filter((f: any) => f !== undefined)

      const cancelPenalties = ratePlans[0]?.cancelpenalties
        ? Array.isArray(ratePlans[0].cancelpenalties)
          ? ratePlans[0].cancelpenalties
          : [ratePlans[0].cancelpenalties]
        : []

      const roomTypeCode = roomRates[0]?.RoomTypeCode || room.roomtypes?.roomtype?.RoomTypeCode

      const roomTypeInfo = parseRoomTypeCode(roomTypeCode)

      const amenities = roomRates[0]?.features?.feature || []
      const amenitiesList = Array.isArray(amenities) ? amenities : [amenities]

      const rateConditions = roomRates[0]?.roomratedescription?.text || []
      const rateConditionsList = Array.isArray(rateConditions) ? rateConditions : [rateConditions]

      const taxes = roomRates[0]?.total?.taxes?.tax || []
      const taxesList = Array.isArray(taxes) ? taxes : [taxes]

      const paymentCards = ratePlans[0]?.guarantee?.[0]?.guaranteesaccepted?.guaranteeaccepted || []
      const paymentCardsList = Array.isArray(paymentCards) ? paymentCards : [paymentCards]

      return {
        rph: room.RPH,
        roomTypeCode: roomTypeCode,
        roomTypeName: roomTypeInfo.roomCategoryName,
        roomTypeDescription: roomTypeInfo.fullDescription,
        bedType: roomTypeInfo.bedTypeName,
        numberOfBeds: roomTypeInfo.numberOfBeds,
        ratePlanCode: ratePlans[0]?.RatePlanCode,
        ratePlanCategory: roomRates[0]?._RatePlanCategory || '',
        rateIndicator: ratePlans[0]?.RateIndicator,
        availabilityStatus: room.AvailabilityStatus,
        bookingCode: roomRates[0]?.BookingCode || '',
        mealsIncluded: {
          breakfast: ratePlans[0]?.mealsincluded?.Breakfast === '1',
          breakfastText:
            ratePlans[0]?.mealsincluded?.Breakfast === '1' ? 'Breakfast Included' : 'Room Only',
          mealPlanIndicator: ratePlans[0]?.mealsincluded?.MealPlanIndicator || ''
        },
        price: {
          amount: parseFloat(room.total?.AmountAfterTax || '0'),
          currencyCode: room.total?.CurrencyCode || 'EUR',
          additionalFeesExcluded: room.total?.AdditionalFeesExcludedIndicator === '1'
        },
        taxes: taxesList.map((tax: any) => ({
          type: tax._Type || '',
          code: tax._Code || '',
          amount: tax._Amount || '',
          percent: tax._Percent || '',
          currencyCode: tax._CurrencyCode || '',
          chargeUnit: tax._ChargeUnit || ''
        })),
        cancelPenalties: cancelPenalties.map((penalty: any) => ({
          cancelPolicyIndicator: penalty.CancelPolicyIndicator,
          policyCode: penalty.cancelpenalty?.PolicyCode || '',
          nonRefundable: penalty.cancelpenalty?.NonRefundable === 'true',
          description: penalty.cancelpenalty?.penaltydescription?.text?._ || ''
        })),

        rateConditions: rateConditionsList.map((text: any) => ({
          text: text._ || '',
          formatted: text.Formatted === '1',
          language: text.Language || 'EN'
        })),

        amenities: amenitiesList.map((amenity: any) => ({
          code: amenity._RoomAmenity || ''
        })),

        paymentMethods: paymentCardsList.slice(1).map((card: any) => ({
          cardCode: card.paymentcard?.CardCode || '',
          cardName: getPaymentCardName(card.paymentcard?.CardCode || '')
        })),
        guarantee: {
          guaranteeCode: ratePlans[0]?.guarantee?.[0]?.GuaranteeCode || '',
          guaranteeType: ratePlans[0]?.guarantee?.[0]?.GuaranteeType || '',
          holdTime: ratePlans[0]?.guarantee?.[1]?.HoldTime || ''
        },
        commission: {
          statusType: ratePlans[0]?.commission?.StatusType || ''
        }
      }
    })

    const roomTypeGroups = processedRooms.reduce((groups, room) => {
      const key = room.roomTypeCode
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(room)
      return groups
    }, {})

    const roomTypes = Object.keys(roomTypeGroups).map((roomTypeCode) => {
      const rooms = roomTypeGroups[roomTypeCode]
      rooms.sort((a, b) => b.price.amount - a.price.amount)
      const firstRoom = rooms[0]
      return {
        roomTypeCode,
        roomTypeName: firstRoom.roomTypeName,
        roomTypeDescription: firstRoom.roomTypeDescription,
        bedType: firstRoom.bedType,
        numberOfBeds: firstRoom.numberOfBeds,
        lowestPrice: Math.min(...rooms.map((r: any) => r.price.amount)),
        highestPrice: Math.max(...rooms.map((r: any) => r.price.amount)),
        currencyCode: firstRoom.price.currencyCode,
        hasBreakfast: rooms.some((r: any) => r.mealsIncluded.breakfast),
        isRefundable: rooms.some((r: any) => !r.cancelPenalties.some((p: any) => p.nonRefundable)),
        rooms
      }
    })

    roomTypes.sort((a, b) => b.lowestPrice - a.lowestPrice)
    const nextSequenceNumber = (parseInt(sequenceNumber) + 1).toString()
    const hotelDescriptiveInfos = await this.gdsDescriptiveInfoOne({
      hotelCode: hotelInfo.hotelCode,
      sessionId,
      sequenceNumber: nextSequenceNumber,
      securityToken
    })

    const result = {
      sessionId,
      sequenceNumber,
      securityToken,
      hotel: {
        ...hotelInfo,
        amenities: hotelDescriptiveInfos?.amenities || [],
        images: hotelDescriptiveInfos?.images || [],
        description: hotelDescriptiveInfos?.description || []
      },
      roomTypes,
      checkinDate,
      checkoutDate,
      adults,
      children: childrens
    }

    writeFileSync('roomSearchFinalResult.json', JSON.stringify(result, null, 2))

    return result
  }

  async gdsDescriptiveInfo(hotelCodes: string[]) {
    const amenitiesCategories = await this.prisma.codeList.findMany({
      where: {
        type: 'HAC'
      }
    })
    const hotelDescriptiveInfos = generateHotelDescriptiveInfos(hotelCodes)
    //let response = JSON.parse(readFileSync('multimediaSearch.json', 'utf8'))
    // if (!response || Object.keys(response).length === 0) {
    const template = getTemplate('multimediaSearch')
    const actionUrl = `${AMADEUS_ACTION_BASE_URL}/Hotel_DescriptiveInfo_13.0`
    const { messageId, timestamp, nonceBase64, passwordDigest } = generateWSSecurityCredentials()
    const payload = template
      .replace('{{messageId}}', messageId)
      .replace('{{ActionUrlHotelMultiSingleAvailability}}', actionUrl)
      .replace('{{apiUrl}}', AMADEUS_API_URL)
      .replace('{{userName}}', AMADEUS_USER_ID)
      .replace('{{nonceBase64}}', nonceBase64)
      .replace('{{passwordDigest}}', passwordDigest)
      .replace('{{agentDutyCode}}', AMADEUS_DUTY_CODE)
      .replace('{{pseudoCityCode}}', AMADEUS_OFFICE_ID)
      .replace('{{createdTimestamp}}', timestamp)
      .replace('{{hotelDescriptiveInfos}}', hotelDescriptiveInfos)

    const response = await postRequest(actionUrl, payload)
    // write response to file
    //writeFileSync('multimediaSearch.json', JSON.stringify(response))
    //}
    const headers = response['soap:envelope']['soap:header']
    const body = response['soap:envelope']['soap:body'].ota_hoteldescriptiveinfors
    const contents = Array.isArray(body.hoteldescriptivecontents.hoteldescriptivecontent)
      ? body.hoteldescriptivecontents.hoteldescriptivecontent
      : [body.hoteldescriptivecontents.hoteldescriptivecontent]

    const results = contents.map((content: any) => {
      let descriptionElement = Array.isArray(
        content.hotelinfo?.descriptions?.multimediadescriptions?.multimediadescription
      )
        ? content.hotelinfo?.descriptions?.multimediadescriptions?.multimediadescription
        : [content.hotelinfo?.descriptions?.multimediadescriptions?.multimediadescription]
      descriptionElement = descriptionElement.filter((f: any) => f !== undefined)

      let textItems = Array.isArray(descriptionElement?.[0]?.textitems?.textitem)
        ? descriptionElement?.[0]?.textitems?.textitem
        : [descriptionElement?.[0]?.textitems?.textitem]

      textItems = textItems.filter((f: any) => f !== undefined)

      let images = Array.isArray(descriptionElement?.[1]?.imageitems?.imageitem)
        ? descriptionElement?.[1]?.imageitems?.imageitem
        : [descriptionElement?.[1]?.imageitems?.imageitem]

      images = images.filter((f: any) => f !== undefined)

      const imageUrls = images.map((image: any) => {
        const imagesList =
          (Array.isArray(image.imageformat) ? image.imageformat : [image.imageformat]) || []
        return imagesList.map((image: any) => {
          return {
            url: image.url
          }
        })
      })

      let amenities = Array.isArray(content.hotelinfo?.services?.service)
        ? content.hotelinfo.services?.service
        : [content.hotelinfo.services?.service]

      amenities = amenities.filter((f: any) => f !== undefined)

      const amenitiesList = amenities.map((amenity: any) => {
        const name = amenitiesCategories.find((a) => a.code === (amenity.Code || ''))?.name || ''
        return {
          amenityCode: amenity.Code || '',
          amenityName: name
        }
      })

      return {
        hotelCode: content.HotelCode,
        description: textItems.map((text: any) => {
          return {
            text: Object.values(text.description)[0]
          }
        }),
        images: imageUrls.flat(),
        amenities: amenitiesList.filter((a: any) => a.amenityCode !== '')
      }
    })
    return results
  }

  async gdsDescriptiveInfoOne(data: DescriptiveRequestDto) {
    const { hotelCode, sessionId, sequenceNumber, securityToken } = data

    const amenitiesCategories = await this.prisma.codeList.findMany({
      where: {
        type: 'HAC'
      }
    })

    // Get template and prepare action URL
    const template = getTemplate('descriptiveRequest')
    const actionUrl = `${AMADEUS_ACTION_BASE_URL}/Hotel_DescriptiveInfo_13.0`

    const { messageId } = generateWSSecurityCredentials()

    const payload = template
      .replace('{{messageId}}', messageId)
      .replace('{{actionUrl}}', actionUrl)
      .replace('{{apiUrl}}', AMADEUS_API_URL)
      .replace('{{sessionId}}', sessionId)
      .replace('{{sequenceNumber}}', sequenceNumber)
      .replace('{{securityToken}}', securityToken)
      .replace('{{hotelCode}}', hotelCode)

    writeFileSync('./temp/descriptiveInfoRequest.xml', payload)

    const response = await postRequest(actionUrl, payload)

    writeFileSync('./temp/descriptiveInfoResponse.json', JSON.stringify(response, null, 2))

    const body = response['soap:envelope']['soap:body'].ota_hoteldescriptiveinfors

    if (body.errors && body.errors.error) {
      console.error('API Error:', body.errors.error.ShortText)
      throw new HttpException(body.errors.error.ShortText, 404)
      //return { error: body.errors.error.ShortText }
    }

    const content = body.hoteldescriptivecontents?.hoteldescriptivecontent
    if (!content) {
      return { hotelCode, description: [], images: {}, amenities: [], guestRooms: [] }
    }

    const hotelBasicInfo = {
      hotelCode: content.HotelCode || '',
      hotelName: content.HotelName || '',
      chainCode: content.ChainCode || '',
      brandName: content.BrandName || '',
      timeZone: content.TimeZone || '',
      currencyCode: content.CurrencyCode || 'EUR',
      whenBuilt: content.hotelinfo?.WhenBuilt || '',
      hotelStatus: content.hotelinfo?.HotelStatusCode || ''
    }

    const descriptionElements = ensureArray(
      content.hotelinfo?.descriptions?.multimediadescriptions?.multimediadescription
    )

    const textItems = []
    descriptionElements.forEach((element) => {
      if (element.InfoCode === '1' && element.textitems?.textitem) {
        const items = ensureArray(element.textitems.textitem)
        textItems.push(...items.filter((item) => item !== undefined))
      }
    })

    // --- get images ---
    const allExtractedImages = []
    const processImageItemsFromPath = (imageItemArray, contextProps = {}) => {
      ensureArray(imageItemArray).forEach((imageItem) => {
        const categoryCode = imageItem.Category || ''
        const categoryName = ImageCategories[categoryCode] || 'Uncategorized'
        const caption =
          imageItem.description?._ || imageItem.description?.Caption || imageItem.description || ''

        ensureArray(imageItem.imageformat)
          .filter((format) => format.url)
          .forEach((format) => {
            allExtractedImages.push({
              url: format.url,
              categoryCode: categoryCode,
              categoryName: categoryName,
              caption: caption,
              width: format.Width || '',
              height: format.Height || '',
              imageFileFormat: format.Format || '',
              dimensionCategory: format.DimensionCategory || '',
              recordId: format.RecordID || '',
              ...contextProps
            })
          })
      })
    }

    // 1. general images from HotelInfo -> Descriptions
    ensureArray(
      content.hotelinfo?.descriptions?.multimediadescriptions?.multimediadescription
    ).forEach((mmd) => {
      processImageItemsFromPath(mmd.imageitems?.imageitem, { sourcePath: 'HotelInfo.Descriptions' })
    })

    // 2. general images for guest room from HotelInfo -> CategoryCodes -> GuestRoomInfo (usually Code="28")
    ensureArray(content.hotelinfo?.categorycodes?.guestroominfo).forEach((gri) => {
      if (gri.multimediadescriptions?.multimediadescription) {
        ensureArray(gri.multimediadescriptions.multimediadescription).forEach((mmd) => {
          processImageItemsFromPath(mmd.imageitems?.imageitem, {
            sourcePath: 'HotelInfo.CategoryCodes.GuestRoomInfo',
            guestRoomInfoCode: gri.Code
          })
        })
      }
    })

    // 3. specific images for each room type from FacilityInfo -> GuestRooms -> GuestRoom
    // For RTMADCMP JSON, this part will not find <imageitems>
    ensureArray(content.facilityinfo?.guestrooms?.guestroom).forEach((room) => {
      const roomTypeCode = room.typeroom?.RoomTypeCode || 'UNKNOWN_ROOM_TYPE'
      const roomName = room.typeroom?.Name || 'Unknown Room Name'
      if (room.multimediadescriptions?.multimediadescription) {
        ensureArray(room.multimediadescriptions.multimediadescription).forEach((mmd) => {
          processImageItemsFromPath(mmd.imageitems?.imageitem, {
            sourcePath: 'FacilityInfo.GuestRoom',
            roomTypeCode: roomTypeCode,
            roomName: roomName
          })
        })
      }
    })

    // 4. meeting room images from FacilityInfo -> MeetingRooms
    ensureArray(content.facilityinfo?.meetingrooms?.meetingroom).forEach((room) => {
      if (room.multimediadescriptions?.multimediadescription) {
        ensureArray(room.multimediadescriptions.multimediadescription).forEach((mmd) => {
          processImageItemsFromPath(mmd.imageitems?.imageitem, {
            sourcePath: 'FacilityInfo.MeetingRoom',
            meetingRoomName: room.RoomName
          })
        })
      }
    })

    // 5. restaurant images from FacilityInfo -> Restaurants
    ensureArray(content.facilityinfo?.restaurants?.restaurant).forEach((restaurant) => {
      if (restaurant.multimediadescriptions?.multimediadescription) {
        ensureArray(restaurant.multimediadescriptions.multimediadescription).forEach((mmd) => {
          processImageItemsFromPath(mmd.imageitems?.imageitem, {
            sourcePath: 'FacilityInfo.Restaurant',
            restaurantName: restaurant.RestaurantName
          })
        })
      }
    })

    // Remove duplicate images based on URL and RecordID (if any)
    const uniqueImages = allExtractedImages.filter(
      (image, index, self) =>
        index ===
        self.findIndex(
          (t) => t.url === image.url && (t.recordId ? t.recordId === image.recordId : true)
        )
    )

    // Group images by categoryName
    const imagesByCategory = uniqueImages.reduce((acc, img) => {
      const category = img.categoryName
      if (!acc[category]) {
        acc[category] = []
      }
      // Only add the necessary properties to the final result
      acc[category].push({
        url: img.url,
        caption: img.caption,
        width: img.width,
        height: img.height,
        imageFileFormat: img.imageFileFormat,
        dimensionCategory: img.dimensionCategory,
        recordId: img.recordId,
        // Add linked information if any (for debugging or more complex logic)
        sourcePath: img.sourcePath,
        roomTypeCodeContext: img.roomTypeCode, // context indicates which room this image belongs to if it is a specific image
        roomNameContext: img.roomName,
        meetingRoomNameContext: img.meetingRoomName,
        restaurantNameContext: img.restaurantName
      })
      return acc
    }, {})
    // --- End of image processing ---

    const amenities = ensureArray(content.hotelinfo?.services?.service)
    const amenitiesList = amenities
      .map((amenity) => {
        const code = amenity.Code || ''
        // Assume amenitiesCategories is an array of objects { code: string, name: string }
        const categoryInfo = amenitiesCategories.find((a) => a.code === code)
        return {
          amenityCode: code,
          amenityName: categoryInfo?.name || amenity.Code || 'Unknown', // Get name from DB or keep the code
          description: amenity.Description || ''
        }
      })
      .filter((a) => a.amenityCode !== '')

    const meetingRooms = ensureArray(content.facilityinfo?.meetingrooms?.meetingroom).map(
      (room) => ({
        roomName: room.RoomName || '',
        capacity: room.MeetingRoomCapacity || '',
        area: room.dimension?.Area || '',
        height: room.dimension?.Height || '',
        unitOfMeasure: room.dimension?.UnitOfMeasureCode || '',
        formats: ensureArray(room.availablecapacities?.meetingroomcapacity).map((format) => ({
          formatCode: format.MeetingRoomFormatCode || '',
          maxOccupancy: format.occupancy?.MaxOccupancy || ''
        })),
        // Add meeting room images (if any and processed above)
        images:
          imagesByCategory[ImageCategories['8']]?.filter(
            (img) => img.meetingRoomNameContext === room.RoomName
          ) || []
      })
    )

    const guestRooms = ensureArray(content.facilityinfo?.guestrooms?.guestroom)
      .map((room) => {
        const roomTypeCode = room.typeroom?.RoomTypeCode || ''
        const roomDescriptionObj =
          room.multimediadescriptions?.multimediadescription?.textitems?.textitem?.description
        const roomDescription =
          typeof roomDescriptionObj === 'object' ? roomDescriptionObj._ : roomDescriptionObj || ''

        // Find specific images for this room type from the processed list
        // (Will be empty for RTMADCMP because JSON does not have images at this path)
        const specificRoomImages = uniqueImages
          .filter(
            (img) =>
              img.sourcePath === 'FacilityInfo.GuestRoom' && img.roomTypeCode === roomTypeCode
          )
          .map((img) => ({
            // Only get the necessary fields for room images
            url: img.url,
            caption: img.caption,
            width: img.width,
            height: img.height,
            dimensionCategory: img.dimensionCategory
          }))

        return {
          maxOccupancy: room.MaxOccupancy || '',
          maxAdultOccupancy: room.MaxAdultOccupancy || '',
          maxChildOccupancy: room.MaxChildOccupancy || '',
          roomTypeName: room.typeroom?.Name || '',
          roomTypeCode: roomTypeCode,
          roomCategory: room.typeroom?.RoomCategory || '',
          bedTypeCode: room.typeroom?.BedTypeCode || '',
          description: roomDescription,
          images: specificRoomImages // Will be empty if no specific images
        }
      })
      .filter((room) => room.roomTypeCode && room.roomTypeCode.toUpperCase() !== 'ALL')

    const restaurants = ensureArray(content.facilityinfo?.restaurants?.restaurant).map(
      (restaurant) => ({
        name: restaurant.RestaurantName || '',
        maxCapacity: restaurant.MaxSeatingCapacity || '',
        proximity: restaurant.ProximityCode || '',
        cuisine: restaurant.cuisinecodes?.cuisinecode?.Code || '',
        isMainCuisine: restaurant.cuisinecodes?.cuisinecode?.IsMain === '1',
        category: restaurant.infocodes?.infocode?.Code || '', // This could be RestaurantCategory
        serviceCode: restaurant.srvcinfocodes?.srvcinfocode?.Code || '',
        operationDays: {
          /* ... keep the logic ... */
        },
        images:
          imagesByCategory[ImageCategories['4']]?.filter(
            (img) => img.restaurantNameContext === restaurant.RestaurantName
          ) || []
      })
    )

    const policies = content.policies
    const policyItems = ensureArray(policies?.policy)

    // Need to check the structure of policyItems to get CheckInTime and CheckOutTime
    // For example: policyItems.find(p => p.policyinfo?.CheckInTime)?.policyinfo.CheckInTime
    const checkInTime =
      policyItems.find((p) => p.policyinfo?.CheckInTime)?.policyinfo.CheckInTime || ''
    const checkOutTime =
      policyItems.find((p) => p.policyinfo?.CheckOutTime)?.policyinfo.CheckOutTime || ''
    const guaranteePolicyDesc =
      ensureArray(
        policyItems.find((p) => p.guaranteepaymentpolicy)?.guaranteepaymentpolicy.guaranteepayment
      )
        .map((gp) => gp.description?.text?._ || gp.description?.text)
        .filter(Boolean)
        .join('; ') || ''
    const cancelPolicyDesc =
      ensureArray(policyItems.find((p) => p.cancelpolicy)?.cancelpolicy.cancelpenalty)
        .map((cp) => cp.penaltydescription?.text?._ || cp.penaltydescription?.text)
        .filter(Boolean)
        .join('; ') || ''

    const paymentCardsList = []
    policyItems.forEach((policy) => {
      if (policy.guaranteepaymentpolicy?.guaranteepayment) {
        ensureArray(policy.guaranteepaymentpolicy.guaranteepayment).forEach((gp) => {
          if (gp.acceptedpayments?.acceptedpayment) {
            ensureArray(gp.acceptedpayments.acceptedpayment).forEach((ap) => {
              if (ap.paymentcard?.CardCode) {
                paymentCardsList.push({
                  cardCode: ap.paymentcard.CardCode,
                  cardName: getPaymentCardName(ap.paymentcard.CardCode) // Assume this function exists
                })
              }
            })
          }
        })
      }
    })
    const uniquePaymentCards = paymentCardsList.filter(
      (card, index, self) => index === self.findIndex((c) => c.cardCode === card.cardCode)
    )

    const contactInfos = ensureArray(content.contactinfos?.contactinfo)
    let phoneNumbers = []
    let emails = []
    let primaryAddress = {}

    contactInfos.forEach((contactInfo) => {
      if (contactInfo.phones?.phone) {
        const phones = ensureArray(contactInfo.phones.phone)
        phoneNumbers.push(
          ...phones.map((phone) => ({
            phoneNumber: phone.PhoneNumber || '',
            phoneType: phone.PhoneTechType || ''
          }))
        )
      }
      if (contactInfo.emails?.email) {
        const emailData = ensureArray(contactInfo.emails.email)
        emails.push(
          ...emailData.map((email) => (typeof email === 'string' ? email : email._)).filter(Boolean)
        )
      }
      // Prioritize the address with UseType="7" (Main Address) or get the first address
      if (
        contactInfo.addresses?.address &&
        (contactInfo.addresses.address.UseType === '7' || Object.keys(primaryAddress).length === 0)
      ) {
        const address = contactInfo.addresses.address
        primaryAddress = {
          addressLine: address.addressline || address.AddressLine || '',
          cityName: address.cityname || address.CityName || '',
          postalCode: address.postalcode || address.PostalCode || '',
          countryName: address.countryname?.Code || address.countryname?._ || ''
        }
      }
    })
    phoneNumbers = phoneNumbers.filter(
      (phone, index, self) => index === self.findIndex((p) => p.phoneNumber === phone.phoneNumber)
    )
    emails = [...new Set(emails)]

    return {
      ...hotelBasicInfo,
      description: textItems.map((text) => ({
        text: text.description?._ || text.description || '', // API may return text.description as string
        title: text.Title || '', // Or get from text.description.Title if it exists
        language: text.description?.Language || 'EN'
      })),
      images: imagesByCategory, // List of images grouped by categoryName
      amenities: amenitiesList,
      meetingRooms,
      guestRooms, // Will contain empty array of images for each room with RTMADCMP JSON
      restaurants,
      policies: {
        checkInTime,
        checkOutTime,
        guaranteePolicy: guaranteePolicyDesc,
        cancellationPolicy: cancelPolicyDesc,
        paymentMethods: uniquePaymentCards
      },
      contactInfo: {
        phoneNumbers,
        emails
      },
      address:
        Object.keys(primaryAddress).length > 0
          ? primaryAddress
          : {
              addressLine: '',
              cityName: '',
              postalCode: '',
              countryName: ''
            }
    }
  }
  async gdsRoomPriceDetail(data: RoomPriceDto) {
    const {
      sessionId,
      sequenceNumber,
      securityToken,
      chainCode,
      hotelCityCode,
      hotelCode,
      checkinDate,
      checkoutDate,
      ratePlanCode,
      roomTypeCode,
      roomQuantity,
      bookingCode,
      adults,
      childrens = []
    } = data

    try {
      // Lấy danh mục tiện ích phòng từ cơ sở dữ liệu
      const amenitiesCategories = await this.prisma.codeList.findMany({
        where: {
          type: 'RMA'
        }
      });
      // Tạo guestCountDetails string từ adults và childrens
      const guestCountDetailsString = generateGuestCountDetails({ count: adults, age: 0 }, childrens)

      // Lấy template enhance_pricing
      const template = getTemplate('enhancePricing')
      const actionUrl = `${AMADEUS_ACTION_BASE_URL}/Hotel_EnhancedPricing_2.0`
      
      // Tạo messageId mới
      const messageId = randomUUID()
      
      // Thay thế các placeholder trong template
      const payload = template
        .replace('{{sessionId}}', sessionId)
        .replace('{{sequenceNumber}}', sequenceNumber)
        .replace('{{securityToken}}', securityToken)
        .replace('{{messageId}}', messageId)
        .replace('{{actionUrl}}', actionUrl)
        .replace('{{apiUrl}}', AMADEUS_API_URL)
        .replace('{{chainCode}}', chainCode)
        .replace('{{hotelCityCode}}', hotelCityCode)
        .replace('{{hotelCode}}', hotelCode)
        .replace('{{checkinDate}}', checkinDate)
        .replace('{{checkoutDate}}', checkoutDate)
        .replace('{{ratePlanCode}}', ratePlanCode)
        .replace('{{roomTypeCode}}', roomTypeCode)
        .replace('{{roomQuantity}}', roomQuantity.toString())
        .replace('{{bookingCode}}', bookingCode)
        .replace('{{guestCountDetails}}', guestCountDetailsString)

      // Ghi request payload ra file để debug
      writeFileSync('enhancePriceRequest.xml', payload)

      // Gửi request đến Amadeus API
      const response = await postRequest(actionUrl, payload)
      
      // Ghi response ra file để debug
      writeFileSync('enhancePriceResponse.json', JSON.stringify(response, null, 2))

      // Xử lý response
      const headers = response['soap:envelope']['soap:header']
      const body = response['soap:envelope']['soap:body'].ota_hotelavailrs

      // Kiểm tra lỗi và xử lý session-related errors
      if (body.errors && body.errors.error) {
        const error = body.errors.error
        const errorMessage = error.ShortText || error._ || 'Unknown API error'
        
        // Check for session-related errors
        if (errorMessage.toLowerCase().includes('session') || 
            errorMessage.toLowerCase().includes('inactive') ||
            errorMessage.toLowerCase().includes('conversation')) {
          
          console.error('Session Error Detected:', errorMessage)
          
          // Return a structured error response for session issues
          throw new HttpException({
            error: 'SESSION_EXPIRED',
            message: 'The session has expired or is inactive. Please start a new search to get fresh session data.',
            details: errorMessage,
            suggestions: [
              'Perform a new hotel search to get valid session tokens',
              'Ensure the session tokens are from a recent search (within 30 minutes)',
              'Check that all session parameters (sessionId, sequenceNumber, securityToken) are correct'
            ]
          }, 400)
        }
        
        // For other API errors
        throw new HttpException({
          error: 'API_ERROR',
          message: errorMessage,
          details: error
        }, 400)
      }

      // Lấy thông tin session mới từ response
      const newSessionId = headers['awsse:session']['awsse:sessionid']
      const newSequenceNumber = headers['awsse:session']['awsse:sequencenumber']
      const newSecurityToken = headers['awsse:session']['awsse:securitytoken']

      // Parse room stays và services
      let roomStays = body.roomstays?.roomstay
      roomStays = Array.isArray(roomStays) ? roomStays : [roomStays]

      let services = body.services?.service || []
      services = Array.isArray(services) ? services : [services]

      // Xử lý room stay data
      const roomStay = roomStays[0]
      if (!roomStay) {
        throw new HttpException('No room availability found', 404)
      }

      // Parse room rate information
      const roomRate = roomStay.roomrates?.roomrate
      const ratePlan = roomStay.rateplans?.rateplan
      const roomType = roomStay.roomtypes?.roomtype

      // Parse pricing details - rates là array trong response
      const rates = roomRate?.rates?.rate
      const processedRates = Array.isArray(rates) ? rates : (rates ? [rates] : [])
      const total = roomRate?.total

      // Parse guest counts
      const guestCounts = roomStay.guestcounts?.guestcount
      const processedGuestCounts = Array.isArray(guestCounts) ? guestCounts : (guestCounts ? [guestCounts] : [])

      // Parse time span
      const timeSpan = roomStay.timespan

      // Parse taxes từ total
      const taxes = total?.taxes?.tax || []
      const processedTaxes = Array.isArray(taxes) ? taxes : (taxes ? [taxes] : [])

      // Parse features/amenities
      const features = roomRate?.features?.feature || []
      const processedFeatures = Array.isArray(features) ? features : (features ? [features] : [])

      // Parse services
      const processedServices = services.map((service: any) => ({
        serviceRPH: service.ServiceRPH || '',
        serviceInventoryCode: service.ServiceInventoryCode || '',
        servicePricingType: service.ServicePricingType || '',
        inclusive: service.Inclusive === '1',
        type: service.Type || '',
        id: service.ID || '',
        price: service.price ? {
          effectiveDate: service.price.EffectiveDate || '',
          expireDate: service.price.ExpireDate || '',
          rateTimeUnit: service.price.RateTimeUnit || '',
          unitMultiplier: service.price.UnitMultiplier || '',
          base: {
            amount: service.price.base?.AmountAfterTax || service.price.base?.AmountBeforeTax || '',
            currencyCode: service.price.base?.CurrencyCode || ''
          }
        } : null,
        serviceDetails: {
          comments: service.servicedetails?.comments ? 
            (Array.isArray(service.servicedetails.comments.comment) ? 
              service.servicedetails.comments.comment : 
              [service.servicedetails.comments.comment]
            ).map((comment: any) => ({
              text: comment.text || comment._ || comment || ''
            })) : [],
          total: service.servicedetails?.total ? {
            amount: service.servicedetails.total.AmountAfterTax || service.servicedetails.total.AmountBeforeTax || '',
            currencyCode: service.servicedetails.total.CurrencyCode || ''
          } : null
        }
      }))

      // Tạo kết quả trả về
      const result = {
        sessionId: newSessionId,
        sequenceNumber: newSequenceNumber,
        securityToken: newSecurityToken,
        roomDetails: {
          roomType: {
            roomType: roomType?.RoomType || '',
            roomTypeCode: roomType?.RoomTypeCode || '',
            isConverted: roomType?.IsConverted === '1'
          },
          ratePlan: {
            ratePlanCode: ratePlan?.RatePlanCode || '',
            rateIndicator: ratePlan?.RateIndicator || '',
            ratePlanName: ratePlan?.RatePlanName || '',
            availabilityStatus: ratePlan?.AvailabilityStatus || '',
            guarantee: {
              guaranteeCode: ratePlan?.guarantee?.GuaranteeCode || '',
              guaranteeType: ratePlan?.guarantee?.GuaranteeType || '',
              holdTime: ratePlan?.guarantee?.HoldTime || '',
              guaranteesAccepted: [] // Không có thông tin guaranteesaccepted trong response
            },
            cancelPenalties: {
              cancelPolicyIndicator: ratePlan?.cancelpenalties?.CancelPolicyIndicator || '',
              cancelPenalty: ratePlan?.cancelpenalties?.cancelpenalty ? {
                policyCode: ratePlan.cancelpenalties.cancelpenalty.PolicyCode || '',
                deadline: ratePlan.cancelpenalties.cancelpenalty.deadline?.AbsoluteDeadline || '',
                amountPercent: {
                  nmbrOfNights: ratePlan.cancelpenalties.cancelpenalty.amountpercent?.NmbrOfNights || '',
                  amount: ratePlan.cancelpenalties.cancelpenalty.amountpercent?.Amount || '',
                  currencyCode: ratePlan.cancelpenalties.cancelpenalty.amountpercent?.CurrencyCode || ''
                }
              } : null
            },
            ratePlanDescription: ratePlan?.rateplandescription?.text || '',
            commission: {
              statusType: ratePlan?.commission?.StatusType || '',
              comment: ratePlan?.commission?.comment?.text || ''
            },
            mealsIncluded: {
              breakfast: '0' // Không có thông tin mealsincluded trong response
            },
            additionalDetails: ratePlan?.additionaldetails?.additionaldetail ? {
              type: ratePlan.additionaldetails.additionaldetail.Type || '',
              description: ratePlan.additionaldetails.additionaldetail.detaildescription?.text || []
            } : null
          },
          roomRate: {
            bookingCode: roomRate?.BookingCode || '',
            roomTypeCode: roomRate?.RoomTypeCode || '',
            numberOfUnits: roomRate?.NumberOfUnits || '',
            ratePlanCode: roomRate?.RatePlanCode || '',
            ratePlanCategory: roomRate?.RatePlanCategory || '',
            availabilityStatus: roomRate?.AvailabilityStatus || '',
            rates: processedRates.map((rate: any) => ({
              effectiveDate: rate.EffectiveDate || '',
              expireDate: rate.ExpireDate || '',
              rateTimeUnit: rate.RateTimeUnit || '',
              minLOS: rate.MinLOS || '',
              maxLOS: rate.MaxLOS || '',
              base: {
                amountBeforeTax: rate.base?.AmountBeforeTax || '',
                currencyCode: rate.base?.CurrencyCode || ''
              },
              paymentPolicies: rate.paymentpolicies?.guaranteepayment ? {
                guaranteePayment: {
                  paymentCode: rate.paymentpolicies.guaranteepayment.PaymentCode || '',
                  guaranteeType: rate.paymentpolicies.guaranteepayment.GuaranteeType || ''
                }
              } : null
            })),
            roomRateDescription: {
              name: roomRate?.roomratedescription?.Name || '',
              text: roomRate?.roomratedescription?.text ? {
                content: roomRate.roomratedescription.text._ || roomRate.roomratedescription.text || '',
                formatted: roomRate.roomratedescription.text.Formatted === '1'
              } : null
            },
            features: processedFeatures.map((feature: any) => {
              // Tìm tên tiện ích từ cơ sở dữ liệu
              const amenityCode = feature.RoomAmenity || ''
              const amenityInfo = amenitiesCategories.find(amenity => amenity.code === amenityCode)
              
              return {
                roomAmenity: amenityCode,
                roomAmenityName: amenityInfo?.name || amenityCode || 'Unknown amenity'
              }
            }),
            total: {
              amountBeforeTax: total?.AmountBeforeTax || '',
              amountAfterTax: total?.AmountAfterTax || '',
              currencyCode: total?.CurrencyCode || '',
              additionalFeesExcludedIndicator: total?.AdditionalFeesExcludedIndicator === '1',
              taxes: processedTaxes.map((tax: any) => ({
                type: tax.Type || '',
                code: tax.Code || '',
                amount: tax.Amount || '',
                percent: tax.Percent || '',
                currencyCode: tax.CurrencyCode || '',
                effectiveDate: tax.EffectiveDate || '',
                expireDate: tax.ExpireDate || '',
                chargeUnit: tax.ChargeUnit || '',
                taxDescription: {
                  name: tax.taxdescription?.Name || '',
                  text: tax.taxdescription?.text?._ || tax.taxdescription?.text || ''
                }
              }))
            },
            // Thêm thông tin total từ roomStay level
            roomStayTotal: {
              amountBeforeTax: roomStay.total?.AmountBeforeTax || '',
              amountAfterTax: roomStay.total?.AmountAfterTax || '',
              currencyCode: roomStay.total?.CurrencyCode || ''
            }
          },
          guestCounts: processedGuestCounts.map((gc: any) => ({
            ageQualifyingCode: gc.AgeQualifyingCode || '',
            count: gc.Count || '',
            age: gc.Age || ''
          })),
          timeSpan: {
            start: timeSpan?.Start || '',
            end: timeSpan?.End || '',
            startDateWindow: {
              dow: timeSpan?.startdatewindow?.DOW || ''
            },
            endDateWindow: {
              dow: timeSpan?.enddatewindow?.DOW || ''
            }
          },
          services: processedServices
        }
      }

      return result

    } catch (error) {
      // Log the detailed error for debugging
      console.error('GDS Room Price Detail Error:', {
        error: error.message,
        sessionId,
        hotelCode,
        ratePlanCode,
        roomTypeCode
      })

      // If it's already an HttpException, re-throw it
      if (error instanceof HttpException) {
        throw error
      }

      // For unexpected errors
      throw new HttpException({
        error: 'SERVICE_ERROR',
        message: 'An unexpected error occurred while fetching room price details',
        details: error.message
      }, 500)
    }
  }
  async gdsUserFillForm(data: UserFillFormDto) {
    const {
      sessionId,
      sequenceNumber,
      securityToken,
      surName,
      firstName,
      email,
      phoneNumber,
      specialRequest,
      smokingCondition
    } = data

    try {
      // Lấy template fillUserInfo
      const template = getTemplate('fillUserInfo')
      const actionUrl = `${AMADEUS_ACTION_BASE_URL}/PNRADD_22_1_1A`
      
      // Tạo messageId mới
      const messageId = randomUUID()
      
      // Tạo smoking condition XML nếu có
      const smokingConditionXml = smokingCondition ? `
                <dataElementsIndiv>
                    <elementManagementData>
                        <reference>
                            <qualifier>OT</qualifier>
                            <number>6</number>
                        </reference>
                        <segmentName>OS</segmentName>
                    </elementManagementData>
                    <freetextData>
                        <freetextDetail>
                            <subjectQualifier>3</subjectQualifier>
                            <type>P27</type>
                        </freetextDetail>
                        <longFreetext>SMOKING ROOM PREFERRED</longFreetext>
                    </freetextData>
                    <referenceForDataElement>
                        <reference>
                            <qualifier>PR</qualifier>
                            <number>1</number>
                        </reference>
                    </referenceForDataElement>
                </dataElementsIndiv>` : ''
      
      // Thay thế các placeholder trong template
      const payload = template
        .replace('{{sessionId}}', sessionId)
        .replace('{{sequenceNumber}}', sequenceNumber)
        .replace('{{securityToken}}', securityToken)
        .replace('{{messageId}}', messageId)
        .replace('{{actionUrl}}', actionUrl)
        .replace('{{apiUrl}}', AMADEUS_API_URL)
        .replace('{{surName}}', surName.toUpperCase())
        .replace('{{firstName}}', firstName.toUpperCase())
        .replace('{{email}}', email.toUpperCase())
        .replace('{{phoneNumber}}', phoneNumber)
        .replace('{{specialRequest}}', specialRequest || 'NO SPECIAL REQUEST')
        .replace('{{smokingCondition}}', smokingConditionXml)

      // Ghi request payload ra file để debug
      writeFileSync('fillUserInfoRequest.xml', payload)

      // Gửi request đến Amadeus API
      const response = await postRequest(actionUrl, payload)
      
      // Ghi response ra file để debug
      writeFileSync('fillUserInfoResponse.json', JSON.stringify(response, null, 2))

      // Xử lý response
      const headers = response['soap:envelope']['soap:header']
      const body = response['soap:envelope']['soap:body'].pnr_reply

      // Kiểm tra lỗi
      if (body.applicationerror) {
        const error = body.applicationerror
        const errorMessage = error.applicationerrordetail?.errormessage || 'Unknown API error'
        
        throw new HttpException({
          error: 'API_ERROR',
          message: errorMessage,
          details: error
        }, 400)
      }

      // Lấy thông tin session mới từ response
      const newSessionId = headers['awsse:session']['awsse:sessionid']
      const newSequenceNumber = headers['awsse:session']['awsse:sequencenumber']
      const newSecurityToken = headers['awsse:session']['awsse:securitytoken']

      // Parse thông tin PNR
      const pnrHeader = body.pnrheader?.reservationinfo?.reservation
      const travellerInfo = body.travellerinfo
      const dataElements = body.dataelementsmaster?.dataelementsindiv || []

      // Xử lý thông tin hành khách
      const passengerData = travellerInfo ? {
        reference: {
          qualifier: travellerInfo.elementmanagementpassenger?.reference?.qualifier || '',
          number: travellerInfo.elementmanagementpassenger?.reference?.number || ''
        },
        segmentName: travellerInfo.elementmanagementpassenger?.segmentname || '',
        lineNumber: travellerInfo.elementmanagementpassenger?.linenumber || '',
        surname: travellerInfo.passengerdata?.travellerinformation?.traveller?.surname || '',
        firstName: travellerInfo.passengerdata?.travellerinformation?.passenger?.firstname || '',
        quantity: travellerInfo.passengerdata?.travellerinformation?.traveller?.quantity || '1'
      } : null

      // Xử lý data elements
      const processedDataElements = Array.isArray(dataElements) ? dataElements : [dataElements]
      const contactInfo = {
        phoneNumber: '',
        email: '',
        specialRequest: '',
        smokingPreference: '',
        ticketInfo: null,
        agentReference: ''
      }

      processedDataElements.forEach((element: any) => {
        if (element.otherdatafreetext) {
          const type = element.otherdatafreetext.freetextdetail?.type
          const text = element.otherdatafreetext.longfreetext || ''
          
          switch (type) {
            case '5': // Phone number
              contactInfo.phoneNumber = text
              break
            case 'P02': // Email
              contactInfo.email = text
              break
            case '28': // Special requests including smoking
              if (text.includes('SMOKING')) {
                contactInfo.smokingPreference = text
              } else {
                contactInfo.specialRequest = text
              }
              break
            case 'P22': // Agent reference
              contactInfo.agentReference = text
              break
          }
        } else if (element.ticketelement) {
          contactInfo.ticketInfo = {
            indicator: element.ticketelement.ticket?.indicator || '',
            date: element.ticketelement.ticket?.date || '',
            officeId: element.ticketelement.ticket?.officeid || ''
          }
        }
      })

      // Parse security information
      const securityInfo = body.securityinformation ? {
        responsibilityInfo: {
          typeOfPnrElement: body.securityinformation.responsibilityinformation?.typeofpnrelement || '',
          officeId: body.securityinformation.responsibilityinformation?.officeid || '',
          iataCode: body.securityinformation.responsibilityinformation?.iatacode || ''
        },
        queueingInfo: {
          queueingOfficeId: body.securityinformation.queueinginformation?.queueingofficeid || ''
        },
        cityCode: body.securityinformation.citycode || ''
      } : null

      // Tạo kết quả trả về
      const result = {
        sessionId: newSessionId,
        sequenceNumber: newSequenceNumber,
        securityToken: newSecurityToken,
        pnrDetails: {
          pnrHeader: {
            companyId: pnrHeader?.companyid || ''
          },
          passenger: passengerData,
          contactInformation: contactInfo,
          securityInformation: securityInfo,
          status: 'SUCCESS',
          message: 'User information has been successfully added to PNR'
        },
        requestData: {
          surname: surName,
          firstName: firstName,
          email: email,
          phoneNumber: phoneNumber,
          specialRequest: specialRequest || 'NO SPECIAL REQUEST',
          smokingCondition: smokingCondition
        }
      }

      return result

    } catch (error) {
      // Log the detailed error for debugging
      console.error('GDS User Fill Form Error:', {
        error: error.message,
        sessionId,
        surname: surName,
        firstName: firstName
      })

      // If it's already an HttpException, re-throw it
      if (error instanceof HttpException) {
        throw error
      }

      // For unexpected errors
      throw new HttpException({
        error: 'SERVICE_ERROR',
        message: 'An unexpected error occurred while filling user information',
        details: error.message
      }, 500)
    }
  }
  async gdsSell(data: HotelSellDto){
    const {
      sessionId,
      sequenceNumber,
      securityToken,
      chainCode,
      hotelCityCode,
      hotelCode,
      guestTattoo,
      bookingCode,
      CC_VENDOR_CODE,
      CC_NUMBER,
      CC_SECURITY_ID,
      CC_EXPIRY_DATE
    } = data

    try{
      // Lấy template và chuẩn bị action URL
      const template = getTemplate('hotelSell')
      const actionUrl = `${AMADEUS_ACTION_BASE_URL}/HBKRCQ_24_2_1A`
      const { messageId } = generateWSSecurityCredentials()

      // Thay thế các placeholder trong template
      const payload = template
        .replace('{{messageId}}', messageId)
        .replace('{{actionUrl}}', actionUrl)
        .replace('{{apiUrl}}', AMADEUS_API_URL)
        .replace('{{sessionId}}', sessionId)
        .replace('{{sequenceNumber}}', sequenceNumber)
        .replace('{{securityToken}}', securityToken)
        .replace('{{chainCode}}', chainCode)
        .replace('{{hotelCityCode}}', hotelCityCode)
        .replace('{{hotelCode}}', hotelCode)
        .replace('{{guestTattoo}}', guestTattoo)
        .replace('{{bookingCode}}', bookingCode)
        .replace('{{CC_VENDOR_CODE}}', CC_VENDOR_CODE)
        .replace('{{CC_NUMBER}}', CC_NUMBER)
        .replace('{{CC_SECURITY_ID}}', CC_SECURITY_ID)
        .replace('{{CC_EXPIRY_DATE}}', CC_EXPIRY_DATE)

      // Ghi request payload ra file để debug
      writeFileSync('hotelSellRequest.xml', payload)

      // Gửi request đến Amadeus API
      const response = await postRequest(actionUrl, payload)

      // Ghi response ra file để debug
      writeFileSync('hotelSellResponse.json', JSON.stringify(response, null, 2))

      // Xử lý response
      const headers = response['soap:envelope']['soap:header']
      const body = response['soap:envelope']['soap:body'].hotel_sellreply

      // Kiểm tra các loại lỗi khác nhau

      // 1. Kiểm tra lỗi từ errorgroup (như trong hotelSellResponse.json)
      if (body.errorgroup) {
        const errorGroup = body.errorgroup
        const messageErrorInfo = errorGroup.messageerrorinformation?.errordetails
        const errorDescription = errorGroup.errordescription

        const errorCode = messageErrorInfo?.errorcode || 'UNKNOWN'
        const errorCategory = messageErrorInfo?.errorcategory || 'UNKNOWN'
        const errorCodeOwner = messageErrorInfo?.errorcodeowner || 'UNKNOWN'
        const errorMessage = errorDescription?.freetext || 'Unknown error occurred'
        const language = errorDescription?.freetextdetails?.language || 'EN'
        const informationType = errorDescription?.freetextdetails?.informationtype || 'UNKNOWN'

        // Xử lý các loại lỗi cụ thể
        let httpStatus = 400
        let errorType = 'API_ERROR'
        let userFriendlyMessage = errorMessage

        switch (errorCode) {
          case '22447': // INVALID VALUE FOUND
            errorType = 'INVALID_VALUE_ERROR'
            userFriendlyMessage = 'Invalid value found in the request. Please check your booking details, credit card information, or session data.'
            httpStatus = 400
            break
          case '11042': // Session related errors
          case '11043':
          case '11044':
            errorType = 'SESSION_ERROR'
            userFriendlyMessage = 'Session has expired or is invalid. Please start a new search to get fresh session data.'
            httpStatus = 401
            break
          case '22448': // Credit card errors
          case '22449':
            errorType = 'PAYMENT_ERROR'
            userFriendlyMessage = 'Invalid credit card information. Please check your card number, expiry date, and security code.'
            httpStatus = 400
            break
          case '22450': // Hotel availability errors
          case '22451':
            errorType = 'AVAILABILITY_ERROR'
            userFriendlyMessage = 'The selected room is no longer available. Please search for alternative options.'
            httpStatus = 409
            break
          case '22452': // Booking errors
          case '22453':
            errorType = 'BOOKING_ERROR'
            userFriendlyMessage = 'Unable to complete the booking. Please try again or contact support.'
            httpStatus = 422
            break
          default:
            errorType = 'UNKNOWN_API_ERROR'
            userFriendlyMessage = `API Error: ${errorMessage}`
            httpStatus = 400
        }

        // Kiểm tra booking status để xác định xem có phòng nào được đặt không
        const bookingStatus = body.bookingtypeindicator?.numberofrooms
        const roomsBooked = parseInt(bookingStatus?.quantity || '0')

        throw new HttpException({
          error: errorType,
          message: userFriendlyMessage,
          details: {
            errorCode,
            errorCategory,
            errorCodeOwner,
            originalMessage: errorMessage,
            language,
            informationType,
            roomsBooked,
            statusCode: bookingStatus?.statuscode || 'UNKNOWN',
            sessionInfo: {
              sessionId: headers['awsse:session']?.['awsse:sessionid'] || sessionId,
              sequenceNumber: headers['awsse:session']?.['awsse:sequencenumber'] || sequenceNumber,
              securityToken: headers['awsse:session']?.['awsse:securitytoken'] || securityToken
            }
          },
          suggestions: this.getErrorSuggestions(errorCode, errorType)
        }, httpStatus)
      }

      // 2. Kiểm tra lỗi từ applicationerror (legacy error format)
      if (body.applicationerror) {
        const error = body.applicationerror
        const errorMessage = error.applicationerrordetail?.errormessage || 'Unknown API error'

        throw new HttpException({
          error: 'APPLICATION_ERROR',
          message: errorMessage,
          details: error,
          suggestions: [
            'Check your request parameters',
            'Verify session tokens are valid',
            'Try the request again with fresh session data'
          ]
        }, 400)
      }

      // 3. Kiểm tra xem có booking thành công không (chỉ khi không có errorgroup)
      const bookingTypeIndicatorCheck = body.bookingtypeindicator
      const numberOfRoomsCheck = bookingTypeIndicatorCheck?.numberofrooms
      const roomsBookedCheck = parseInt(numberOfRoomsCheck?.quantity || '0')

      if (roomsBookedCheck === 0) {
        throw new HttpException({
          error: 'BOOKING_FAILED',
          message: 'No rooms were booked. The booking request failed.',
          details: {
            quantity: numberOfRoomsCheck?.quantity || '0',
            statusCode: numberOfRoomsCheck?.statuscode || 'UNKNOWN',
            possibleReasons: [
              'Room no longer available',
              'Invalid booking parameters',
              'Payment information rejected',
              'Hotel booking restrictions'
            ]
          },
          suggestions: [
            'Try searching for the hotel again to get fresh availability',
            'Check if the room is still available',
            'Verify your payment information',
            'Contact the hotel directly for availability'
          ]
        }, 409)
      }

      // Lấy thông tin session mới từ response
      const newSessionId = headers['awsse:session']['awsse:sessionid']
      const newSequenceNumber = headers['awsse:session']['awsse:sequencenumber']
      const newSecurityToken = headers['awsse:session']['awsse:securitytoken']

      // Parse booking information
      const bookingTypeIndicator = body.bookingtypeindicator
      const roomStayData = body.roomstaydata

      // Parse booking status
      const numberOfRooms = bookingTypeIndicator?.numberofrooms
      const bookingStatus = {
        quantity: numberOfRooms?.quantity || '',
        statusCode: numberOfRooms?.statuscode || ''
      }

      // Parse PNR information
      const pnrInfo = roomStayData?.pnrinfo?.tattooreference?.referencedetails
      const pnrReference = {
        type: pnrInfo?.type || '',
        value: pnrInfo?.value || ''
      }

      // Parse global booking information
      const globalBookingInfo = roomStayData?.globalbookinginfo
      const hotelPropertyInfo = globalBookingInfo?.hotelpropertyinfo
      const hotelReference = hotelPropertyInfo?.hotelreference

      const hotelInfo = {
        chainCode: hotelReference?.chaincode || '',
        cityCode: hotelReference?.citycode || '',
        hotelCode: hotelReference?.hotelcode || '',
        hotelName: hotelPropertyInfo?.hotelname || ''
      }

      // Parse force sell indicator
      const forceSellIndicator = globalBookingInfo?.forcesellindicator?.statusdetails
      const forceSellInfo = {
        indicator: forceSellIndicator?.indicator || '',
        action: forceSellIndicator?.action || ''
      }

      // Parse company information
      const companyInfo = {
        companyName: globalBookingInfo?.individualcompanyid?.companyname || ''
      }

      // Parse reservation information
      const reservationInfo = globalBookingInfo?.bookinginfo?.reservation
      const reservation = {
        companyId: reservationInfo?.companyid || '',
        controlNumber: reservationInfo?.controlnumber || '',
        controlType: reservationInfo?.controltype || ''
      }

      // Parse booking source
      const bookingSource = globalBookingInfo?.bookingsource?.originidentification
      const originInfo = {
        originatorId: bookingSource?.originatorid || ''
      }

      // Parse global price information
      const globalPriceInfo = globalBookingInfo?.globalpriceinformation?.globalprice?.tariffinfo
      const priceInfo = {
        amount: globalPriceInfo?.amount || '',
        currency: globalPriceInfo?.currency || '',
        dailyTotalIndicator: globalPriceInfo?.dailytotalindicator || '',
        totalAmount: globalPriceInfo?.totalamount || ''
      }

      // Parse representative parties (guest information)
      const representativeParties = globalBookingInfo?.representativeparties
      const occupantList = representativeParties?.occupantlist?.passengerreference
      const guestContactInfo = representativeParties?.guestcontactinfo
      const occupantPreferences = representativeParties?.occupantpreferences?.occupantpreferences

      const guestInfo = {
        passengerReference: {
          type: occupantList?.type || '',
          value: occupantList?.value || ''
        },
        contactInfo: {
          phoneOrEmailType: guestContactInfo?.phoneoreailtype || '',
          emailAddress: guestContactInfo?.emailaddress || ''
        },
        preferences: {
          occupantLanguage: occupantPreferences?.occupantlanguage || ''
        }
      }

      // Parse room list information
      const roomListInfo = roomStayData?.roomlistinfo
      const roomStayIndex = roomListInfo?.roomstayindex?.sequencedetails
      const markLinesAndRateDesc = roomListInfo?.marklinesandratedesc

      const roomInfo = {
        roomStayIndex: {
          number: roomStayIndex?.number || ''
        },
        rateDescription: {
          textSubjectQualifier: markLinesAndRateDesc?.freetextqualification?.textsubjectqualifier || '',
          informationTypeId: markLinesAndRateDesc?.freetextqualification?.informationtypeid || '',
          freeText: markLinesAndRateDesc?.freetext || ''
        }
      }

      // Parse commission and markup
      const commissionAndMarkup = roomListInfo?.commissionandmarkup?.commissioninfo?.commissiondetails
      const commissionInfo = {
        type: commissionAndMarkup?.type || ''
      }

      // Parse requestable information
      const requestableInfo = roomListInfo?.requestableinformation
      const requestedDates = requestableInfo?.requesteddates
      const roomRateDetails = requestableInfo?.roomratedetails

      const dateInfo = {
        businessSemantic: requestedDates?.businesssemantic || '',
        timeMode: requestedDates?.timemode || '',
        checkInDate: {
          year: requestedDates?.begindatetime?.year || '',
          month: requestedDates?.begindatetime?.month || '',
          day: requestedDates?.begindatetime?.day || ''
        },
        checkOutDate: {
          year: requestedDates?.enddatetime?.year || '',
          month: requestedDates?.enddatetime?.month || '',
          day: requestedDates?.enddatetime?.day || ''
        }
      }

      // Parse room rate details
      const roomInformation = roomRateDetails?.roominformation
      const roomRateIdentifier = roomInformation?.roomrateidentifier
      const guestCountDetails = roomInformation?.guestcountdetails

      const roomRateInfo = {
        roomType: roomRateIdentifier?.roomtype || '',
        ratePlanCode: roomRateIdentifier?.rateplancode || '',
        rateCategoryCode: roomRateIdentifier?.ratecategorycode || '',
        rateQualifiedIndic: Array.isArray(roomRateIdentifier?.ratequalifiedindic)
          ? roomRateIdentifier.ratequalifiedindic
          : (roomRateIdentifier?.ratequalifiedindic ? [roomRateIdentifier.ratequalifiedindic] : []),
        bookingCode: roomInformation?.bookingcode || '',
        guestCount: {
          numberOfUnit: guestCountDetails?.numberofunit || '',
          unitQualifier: guestCountDetails?.unitqualifier || ''
        }
      }

      // Parse special information
      const specialInfos = roomRateDetails?.specialinfo
      const processedSpecialInfos = Array.isArray(specialInfos) ? specialInfos : (specialInfos ? [specialInfos] : [])
      const specialInformation = processedSpecialInfos.map((info: any) => ({
        textSubjectQualifier: info.freetextdetails?.textsubjectqualifier || '',
        informationType: info.freetextdetails?.informationtype || '',
        source: info.freetextdetails?.source || '',
        encoding: info.freetextdetails?.encoding || '',
        freeText: info.freetext || ''
      }))

      // Parse booking requirements
      const bookingRequirement = roomRateDetails?.bookingrequirement
      const guaranteeDepositStatusInfo = bookingRequirement?.guaranteedepositstatusinfo?.statusdetails
      const holdTime = bookingRequirement?.holdtime?.ruledetails
      const paymentInformation = bookingRequirement?.paymentinformation?.paymentdetails

      const bookingRequirements = {
        guaranteeDeposit: {
          indicator: guaranteeDepositStatusInfo?.indicator || '',
          action: guaranteeDepositStatusInfo?.action || ''
        },
        holdTime: {
          type: holdTime?.type || '',
          quantity: holdTime?.quantity || '',
          quantityUnit: holdTime?.quantityunit || ''
        },
        payment: {
          formOfPaymentCode: paymentInformation?.formofpaymentcode || '',
          paymentType: paymentInformation?.paymenttype || '',
          serviceToPay: paymentInformation?.servicetopay || ''
        }
      }

      // Parse credit card information
      const creditCardInfos = bookingRequirement?.creditcardinformation
      const processedCreditCards = Array.isArray(creditCardInfos) ? creditCardInfos : (creditCardInfos ? [creditCardInfos] : [])
      const acceptedCreditCards = processedCreditCards.map((card: any) => ({
        type: card.formofpayment?.type || '',
        vendorCode: card.formofpayment?.vendorcode || ''
      }))

      // Parse tariff information
      const tariffInformation = roomRateDetails?.tariffinformation?.tariffinfo
      const tariffInfo = {
        amount: tariffInformation?.amount || '',
        currency: tariffInformation?.currency || '',
        dailyTotalIndicator: tariffInformation?.dailytotalindicator || '',
        status: tariffInformation?.status || ''
      }

      // Parse guarantee or deposit information
      const guaranteeOrDeposit = requestableInfo?.guaranteeordeposit
      const paymentInfo = guaranteeOrDeposit?.paymentinfo?.paymentdetails
      const groupCreditCardInfo = guaranteeOrDeposit?.groupcreditcardinfo

      const guaranteeInfo = {
        payment: {
          formOfPaymentCode: paymentInfo?.formofpaymentcode || '',
          paymentType: paymentInfo?.paymenttype || '',
          serviceToPay: paymentInfo?.servicetopay || ''
        },
        creditCard: {
          vendorCode: groupCreditCardInfo?.creditcardinfo?.ccinfo?.vendorcode || '',
          cardNumber: groupCreditCardInfo?.creditcardinfo?.ccinfo?.cardnumber || '',
          expiryDate: groupCreditCardInfo?.creditcardinfo?.ccinfo?.expirydate || '',
          concealedCardNumber: groupCreditCardInfo?.concealedcreditcardinfo?.ccinfo?.cardnumber || '',
          fortknoxId: {
            type: groupCreditCardInfo?.fortknoxids?.referencedetails?.type || '',
            value: groupCreditCardInfo?.fortknoxids?.referencedetails?.value || ''
          }
        }
      }

      // Parse guest list
      const guestList = requestableInfo?.guestlist?.occupantlist?.passengerreference
      const guestListInfo = {
        type: guestList?.type || '',
        value: guestList?.value || ''
      }

      // Parse rate changes
      const rateChanges = roomListInfo?.ratechanges
      const rateChangeAmountInfo = rateChanges?.ratechangeamountinformation?.tariffinfo
      const rateChangePeriodInfo = rateChanges?.ratechangeperiodinformation

      const rateChangeInfo = {
        amount: {
          amount: rateChangeAmountInfo?.amount || '',
          currency: rateChangeAmountInfo?.currency || '',
          dailyTotalIndicator: rateChangeAmountInfo?.dailytotalindicator || ''
        },
        period: {
          businessSemantic: rateChangePeriodInfo?.businesssemantic || '',
          beginDate: {
            year: rateChangePeriodInfo?.begindatetime?.year || '',
            month: rateChangePeriodInfo?.begindatetime?.month || '',
            day: rateChangePeriodInfo?.begindatetime?.day || ''
          },
          endDate: {
            year: rateChangePeriodInfo?.enddatetime?.year || '',
            month: rateChangePeriodInfo?.enddatetime?.month || '',
            day: rateChangePeriodInfo?.enddatetime?.day || ''
          }
        }
      }

      // Tạo kết quả trả về
      const result = {
        sessionId: newSessionId,
        sequenceNumber: newSequenceNumber,
        securityToken: newSecurityToken,
        bookingDetails: {
          status: bookingStatus,
          pnrReference: pnrReference,
          hotelInfo: hotelInfo,
          forceSellInfo: forceSellInfo,
          companyInfo: companyInfo,
          reservation: reservation,
          originInfo: originInfo,
          priceInfo: priceInfo,
          guestInfo: guestInfo,
          roomInfo: roomInfo,
          commissionInfo: commissionInfo,
          dateInfo: dateInfo,
          roomRateInfo: roomRateInfo,
          specialInformation: specialInformation,
          bookingRequirements: bookingRequirements,
          acceptedCreditCards: acceptedCreditCards,
          tariffInfo: tariffInfo,
          guaranteeInfo: guaranteeInfo,
          guestListInfo: guestListInfo,
          rateChangeInfo: rateChangeInfo
        },
        requestData: {
          sessionId,
          sequenceNumber,
          securityToken,
          chainCode,
          hotelCityCode,
          hotelCode,
          guestTattoo,
          bookingCode,
          creditCard: {
            vendorCode: CC_VENDOR_CODE,
            number: CC_NUMBER.replace(/(.{4})(.*)(.{4})/, '$1****$3'), // Mask card number for security
            securityId: '***', // Mask security code
            expiryDate: CC_EXPIRY_DATE
          }
        },
        message: 'Hotel booking has been successfully processed'
      }

      return result

    } catch (error) {
      // Log the detailed error for debugging
      console.error('GDS Sell Error:', {
        error: error.message,
        sessionId,
        hotelCode
      })

      // If it's already an HttpException, re-throw it
      if (error instanceof HttpException) {
        throw error
      }

      // For unexpected errors
      throw new HttpException({
        error: 'SERVICE_ERROR',
        message: 'An unexpected error occurred while selling hotel',
        details: error.message
      }, 500)
    }
  }

  /**
   * Get error suggestions based on error code and type
   * @param errorCode - The Amadeus API error code
   * @param errorType - The categorized error type
   * @returns Array of suggestion strings
   */
  private getErrorSuggestions(errorCode: string, errorType: string): string[] {
    const commonSuggestions = [
      'Ensure all required fields are provided',
      'Verify that session tokens are from a recent search (within 30 minutes)',
      'Check that the hotel and room are still available'
    ]

    switch (errorType) {
      case 'INVALID_VALUE_ERROR':
        return [
          'Check that all booking parameters are correct and properly formatted',
          'Verify credit card number, expiry date (MMYY format), and security code',
          'Ensure guest tattoo and booking code match the previous search results',
          'Validate that chain code, hotel city code, and hotel code are correct',
          ...commonSuggestions
        ]

      case 'SESSION_ERROR':
        return [
          'Start a new hotel search to get fresh session tokens',
          'Ensure you are using the latest sessionId, sequenceNumber, and securityToken',
          'Complete the booking process within 30 minutes of the initial search',
          'Do not reuse session tokens from previous booking attempts'
        ]

      case 'PAYMENT_ERROR':
        return [
          'Verify credit card number is correct and contains only digits',
          'Check expiry date is in MMYY format (e.g., 1227 for December 2027)',
          'Ensure security code (CVV) is 3-4 digits',
          'Confirm the credit card vendor code matches the card type (VI=Visa, MC=MasterCard, AX=Amex)',
          'Try using a different credit card if the current one is declined'
        ]

      case 'AVAILABILITY_ERROR':
        return [
          'Search for the hotel again to check current availability',
          'Try selecting a different room type or rate plan',
          'Consider adjusting your check-in or check-out dates',
          'Look for alternative hotels in the same area'
        ]

      case 'BOOKING_ERROR':
        return [
          'Wait a few minutes and try the booking again',
          'Ensure all guest information has been properly filled',
          'Check that the selected room meets the hotel\'s booking requirements',
          'Contact customer support if the problem persists'
        ]

      default:
        return [
          'Review all request parameters for accuracy',
          'Try the request again with fresh session data',
          'Contact technical support if the error persists',
          ...commonSuggestions
        ]
    }
  }

}
