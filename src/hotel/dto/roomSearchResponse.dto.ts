import { ApiProperty } from '@nestjs/swagger'

export class RoomSearchDto {
  @ApiProperty({
    description: 'The session id of the hotel',
    example: '1234567890'
  })
  sessionId: string

  @ApiProperty({
    description: 'The session id of the hotel',
    example: '1234567890'
  })
  securityToken: string

  @ApiProperty({
    description: 'The rate plan code of the hotel',
    example: 'MAD'
  })
  ratePlanCode: string

  @ApiProperty({
    description: 'The hotel code of the hotel',
    example: 'MAD'
  })
  hotelCode: string

  @ApiProperty({
    description: 'The room type code of the hotel',
    example: 'MAD'
  })
  roomTypeCode: string

  @ApiProperty({
    description: 'The booking code of the hotel',
    example: 'MAD'
  })
  bookingCode: string
}
