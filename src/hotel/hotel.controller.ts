import { Controller, Post, Body, Query, Get, HttpCode, HttpStatus } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBody } from '@nestjs/swagger'
import { HotelService } from './hotel.service'
import { GeneralSearchDto } from './dto/generalSearch.dto'
import { RoomPriceDto } from './dto/roomPrice.dto'
import { UserFillFormDto } from './dto/userFillForm.dto'
import { HotelSellDto } from './dto/hotellSell.dto'
import { SystemConfirmDto } from './dto/systemConfirm.dto'
import { PnrRetrieveDto } from './dto/pnrRetrieve.dto'
import { DescriptiveRequestDto } from './dto/descriptiveRequest.dto'

@Controller('hotel')
export class HotelController {
  constructor(private readonly hotelService: HotelService) {}

  @Get('search')
  async search(@Query('keyword') keyword: string) {
    return await this.hotelService.search(keyword)
  }

  @Post('general-search')
  async generalSearch(@Body() data: GeneralSearchDto) {
    return await this.hotelService.gdsGeneralSearch(data)
  }

  @Post('room-search')
  async roomSearch(@Body() data: GeneralSearchDto) {
    return await this.hotelService.gdsRoomSearch(data)
  }

  @Post('descriptive-info')
  async descriptiveInfo(@Query('hotelCode') hotelCode: string) {
    return await this.hotelService.gdsDescriptiveInfo([hotelCode])
  }

  @Post('room-price-detail')
  async roomPriceDetail(@Body() data: RoomPriceDto) {
    return await this.hotelService.gdsRoomPriceDetail(data)
  }

  @Post('fill-user-info')
  async fillUserInfo(@Body() data: UserFillFormDto) {
    return await this.hotelService.gdsUserFillForm(data)
  }
  @Post('hotel-sell')
  async hotelSell(@Body() data: HotelSellDto) {
    return await this.hotelService.gdsSell(data)
  }

  @Post('system-confirm')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'System confirm PNR',
    description: 'Finalize and confirm the hotel booking PNR in the system'
  })
  @ApiBody({ type: SystemConfirmDto })
  @ApiResponse({
    status: 200,
    description: 'PNR successfully confirmed and saved',
    schema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        sequenceNumber: { type: 'string' },
        securityToken: { type: 'string' },
        pnrDetails: {
          type: 'object',
          properties: {
            pnrInfo: {
              type: 'object',
              properties: {
                companyId: { type: 'string', example: 'RT' },
                controlNumber: { type: 'string', example: '3753ZF3500' },
                date: { type: 'string', example: '241220' },
                time: { type: 'string', example: '1030' }
              }
            },
            status: { type: 'string', example: 'CONFIRMED' },
            message: { type: 'string', example: 'PNR has been successfully confirmed and saved' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request or session expired',
    schema: {
      type: 'object',
      properties: {
        error: { type: 'string', example: 'API_ERROR' },
        message: { type: 'string', example: 'Session has expired or invalid session data' }
      }
    }
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async systemConfirm(@Body() data: SystemConfirmDto) {
    return await this.hotelService.gdsSystemConfirm(data)
  }

  
  @Post('pnr-retrieve')
  async pnrRetrieve(@Body() data: PnrRetrieveDto) {
    return await this.hotelService.gdsPnrRetrieve(data)
  }

}
