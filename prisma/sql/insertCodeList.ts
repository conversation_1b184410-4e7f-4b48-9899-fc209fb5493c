import { PrismaClient, Prisma } from '@prisma/client'
import fs from 'fs'
import { parse } from 'csv-parse'
import { createReadStream } from 'fs'
import { join } from 'path'

const prisma = new PrismaClient()

async function importCSV() {
  const codeList = new Set()

  // Parse CSV
  const filePath = join(__dirname, '..', '..', 'prisma', 'sql', 'codeList.csv')
  createReadStream(filePath)
    .pipe(parse({ columns: true }))
    .on('data', (row) => {
      codeList.add(
        JSON.stringify({
          code: row.Code,
          name: row.Name
        })
      )
    })
    .on('end', async () => {
      const datum = Array.from(codeList).map((c) => JSON.parse(c as string))
      const insertData: Prisma.CodeListCreateManyInput[] = []
      let type = ''
      let count = 0
      for (const item of datum) {
        if (!item.name) {
          type = item.code
          continue
        }

        insertData.push({
          code: item.code,
          name: item.name,
          description: item.name,
          type: type
        })
      }

      console.log(insertData)

      await prisma.codeList.createMany({
        data: insertData,
        skipDuplicates: true
      })

      console.log('Data imported successfully')
      await prisma.$disconnect()
    })
}

importCSV().catch(console.error)
