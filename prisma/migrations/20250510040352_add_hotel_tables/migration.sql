-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "birth_date" TIMESTAMP(3),
    "gender" INTEGER,
    "referral_code" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Chain" (
    "chain_code" TEXT NOT NULL,
    "chain_name" TEXT NOT NULL,

    CONSTRAINT "Chain_pkey" PRIMARY KEY ("chain_code")
);

-- CreateTable
CREATE TABLE "Country" (
    "country_code" TEXT NOT NULL,
    "country_name" TEXT NOT NULL,

    CONSTRAINT "Country_pkey" PRIMARY KEY ("country_code")
);

-- CreateTable
CREATE TABLE "Hotel" (
    "property_code" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "property_identifier" TEXT NOT NULL,
    "property_name" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "transportation" TEXT NOT NULL,
    "address_line_1" TEXT NOT NULL,
    "address_line_2" TEXT,
    "city_2" TEXT NOT NULL,
    "state_code" TEXT,
    "zip_code" TEXT,
    "phone" TEXT NOT NULL,
    "fax" TEXT,
    "local_rating" INTEGER,
    "self_rating" TEXT,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,
    "dupe_pool_id" TEXT NOT NULL,
    "provider_value" TEXT,
    "last_update_timestamp" TIMESTAMP(3) NOT NULL,
    "chain_code" TEXT NOT NULL,
    "country_code" TEXT NOT NULL,

    CONSTRAINT "Hotel_pkey" PRIMARY KEY ("property_code")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- AddForeignKey
ALTER TABLE "Hotel" ADD CONSTRAINT "Hotel_chain_code_fkey" FOREIGN KEY ("chain_code") REFERENCES "Chain"("chain_code") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Hotel" ADD CONSTRAINT "Hotel_country_code_fkey" FOREIGN KEY ("country_code") REFERENCES "Country"("country_code") ON DELETE RESTRICT ON UPDATE CASCADE;
