import { INestApplication, RequestMethod, VersioningType } from '@nestjs/common'
import * as dotenv from 'dotenv'
dotenv.config()
export const RPC_URL = process.env.RPC_URL || ''

export const JWT_SECRET = process.env.JWT_SECRET || 'secret'
export const JWT_EXP = process.env.JWT_EXP || '1h'

export const ROUND_INTERVAL = 5 //MINUTES

export const HOST = process.env.HOST || ''

export const MODE = process.env.MODE || 'dev'

export const GOOGLE_MAILER_CLIENT_ID = process.env.GOOGLE_MAILER_CLIENT_ID || ''
export const GOOGLE_MAILER_CLIENT_SECRET = process.env.GOOGLE_MAILER_CLIENT_SECRET || ''
export const GOOGLE_MAILER_REFRESH_TOKEN = process.env.GOOGLE_MAILER_REFRESH_TOKEN || ''
export const GOOGLE_AUTH_REDIRECT = process.env.GOOGLE_AUTH_REDIRECT || ''

export const REDIS_HOST = process.env.REDIS_HOST || 'localhost'
export const REDIS_PORT = process.env.REDIS_PORT || 6379
export const REDIS_USER = process.env.REDIS_USER || ''
export const REDIS_PASSWORD = process.env.REDIS_PASSWORD || ''

export const FE_URL = process.env.FE_URL || ''

export const BASE_RPC_URL = process.env.BASE_RPC_URL || ''

export const APM_SERVER = process.env.APM_SERVER || 'http://localhost:8200'
export const APM_SECRET_TOKEN = process.env.APM_SECRET_TOKEN || ''
export const NODE_ENV = process.env.NODE_ENV || 'development'
export const ELASTIC_SERVICE_NAME = process.env.ELASTIC_SERVICE_NAME || 'travel-api'

export const AMADEUS_API_URL = process.env.AMADEUS_API_URL || ''
export const AMADEUS_ACTION_BASE_URL = process.env.AMADEUS_ACTION_BASE_URL || ''
export const AMADEUS_WSAP = process.env.AMADEUS_WSAP || ''
export const AMADEUS_USER_ID = process.env.AMADEUS_USER_ID || ''
export const AMADEUS_OFFICE_ID = process.env.AMADEUS_OFFICE_ID || ''
export const AMADEUS_DUTY_CODE = process.env.AMADEUS_DUTY_CODE || ''
export const AMADEUS_USERNAME = process.env.AMADEUS_USERNAME || ''
export const AMADEUS_PASSWORD = process.env.AMADEUS_PASSWORD || ''
export const AMADEUS_PASSWORD_BASE64 = process.env.AMADEUS_PASSWORD_BASE64 || ''

export function setAppSetting(app: INestApplication) {
  app.setGlobalPrefix('api', {
    exclude: [{ path: 'health', method: RequestMethod.GET }]
  })
  app.enableVersioning({
    type: VersioningType.URI
  })
  app.enableCors({
    origin: [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://dev.runx.app',
      'https://runx.app',
      'https://preorder.runx.app',
      'http://localhost:5173',
      'https://admin.runx.app',
      'https://order-testnet.runx.app',
      'https://order.runx.app'
    ],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    optionsSuccessStatus: 204
  })
}
