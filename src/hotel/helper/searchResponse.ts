import { parseStringPromise } from 'xml2js'

// Interface for HotelStay (from previous hotelStayXmlToObject.ts)
interface Address {
  AddressLine: string
  CityName: string
  PostalCode: string
  CountryName: {
    Code: string
    $t: string
  }
}

interface Position {
  Latitude: string
  Longitude: string
}

interface Transportation {
  TransportationCode: string
}

interface Transportations {
  Transportation: Transportation | Transportation[]
}

interface RelativePosition {
  Transportations: Transportations
}

interface BasicPropertyInfo {
  ChainCode: string
  HotelCode: string
  HotelCityCode: string
  HotelName: string
  HotelCodeContext: string
  ChainName: string
  AreaID: string
  SupplierIntegrationLevel: string
  Position: Position
  Address: Address
  RelativePosition: RelativePosition
}

interface HotelStay {
  RoomStayRPH: string
  BasicPropertyInfo: BasicPropertyInfo
}

// Interface for RoomStay (from previous roomStaysXmlToObject.ts)
interface Text {
  Formatted: string
  Language: string
  $t: string
}

interface RatePlanDescription {
  Text: Text
}

interface Guarantee {
  HoldTime: string
}

interface Commission {
  StatusType: string
}

interface MealsIncluded {
  Breakfast: string
  MealPlanIndicator: string
}

interface RatePlan {
  RatePlanCode: string
  RateIndicator: string
  RatePlanName: string
  AvailabilityStatus: string
  Guarantee: Guarantee
  RatePlanDescription: RatePlanDescription
  Commission: Commission
  MealsIncluded: MealsIncluded
}

interface Base {
  AmountAfterTax: string
  CurrencyCode: string
}

interface GuaranteePayment {
  HoldTime: string
}

interface PaymentPolicies {
  GuaranteePayment: GuaranteePayment
}

interface Rate {
  EffectiveDate?: string
  ExpireDate?: string
  RateTimeUnit?: string
  Base?: Base
  PaymentPolicies?: PaymentPolicies
  Total?: Base
}

interface RoomRateDescription {
  Name: string
  CreatorID: string
  Text: Text[]
}

interface Total {
  AmountAfterTax: string
  CurrencyCode: string
}

interface RoomRate {
  BookingCode: string
  RoomTypeCode: string
  NumberOfUnits: string
  RatePlanCode: string
  AvailabilityStatus: string
  Rates: { Rate: Rate[] }
  RoomRateDescription: RoomRateDescription
  Total: Total
}

interface GuestCount {
  AgeQualifyingCode: string
  Count: string
  Age?: string
}

interface StartDateWindow {
  DOW: string
}

interface EndDateWindow {
  DOW: string
}

interface TimeSpan {
  Start: string
  End: string
  StartDateWindow: StartDateWindow
  EndDateWindow: EndDateWindow
}

interface ServiceRPH {
  RPH: string
}

interface RoomStay {
  MarketCode: string
  AvailabilityStatus: string
  InfoSource: string
  RPH: string
  RatePlans: { RatePlan: RatePlan }
  RoomRates: { RoomRate: RoomRate }
  GuestCounts: { GuestCount: GuestCount[] }
  TimeSpan: TimeSpan
  Total: Total
  ServiceRPHs: { ServiceRPH: ServiceRPH[] }
}

// Interface for Service (from previous servicesXmlToObject.ts)
interface Comment {
  Name: string
}

interface Comments {
  Comment: Comment | Comment[]
}

interface ServiceDetails {
  Comments: Comments
  Total?: Total
}

interface Price {
  EffectiveDate: string
  ExpireDate: string
  RateTimeUnit: string
  UnitMultiplier: string
  Base: Base
}

interface Service {
  ServicePricingType?: string
  ServiceRPH: string
  ServiceInventoryCode: string
  Inclusive: string
  Quantity: string
  Type: string
  ID: string
  Price?: Price
  ServiceDetails: ServiceDetails
}

// Top-level interface for OTA_HotelAvailRS
interface OTAHotelAvailRS {
  HotelStays: HotelStay[]
  RoomStays: RoomStay[]
  Services: Service[]
}

// Function to parse HotelStay
async function parseHotelStays(hotelStaysData: any): Promise<HotelStay[]> {
  const mapHotelStay = (data: any): HotelStay => ({
    RoomStayRPH: data.RoomStayRPH,
    BasicPropertyInfo: {
      ChainCode: data.BasicPropertyInfo.ChainCode,
      HotelCode: data.BasicPropertyInfo.HotelCode,
      HotelCityCode: data.BasicPropertyInfo.HotelCityCode,
      HotelName: data.BasicPropertyInfo.HotelName,
      HotelCodeContext: data.BasicPropertyInfo.HotelCodeContext,
      ChainName: data.BasicPropertyInfo.ChainName,
      AreaID: data.BasicPropertyInfo.AreaID,
      SupplierIntegrationLevel: data.BasicPropertyInfo.SupplierIntegrationLevel,
      Position: {
        Latitude: data.BasicPropertyInfo.Position.Latitude,
        Longitude: data.BasicPropertyInfo.Position.Longitude
      },
      Address: {
        AddressLine: data.BasicPropertyInfo.Address.AddressLine,
        CityName: data.BasicPropertyInfo.Address.CityName,
        PostalCode: data.BasicPropertyInfo.Address.PostalCode,
        CountryName: {
          Code: data.BasicPropertyInfo.Address.CountryName.Code,
          $t: data.BasicPropertyInfo.Address.CountryName._
        }
      },
      RelativePosition: {
        Transportations: {
          Transportation: Array.isArray(
            data.BasicPropertyInfo.RelativePosition.Transportations.Transportation
          )
            ? data.BasicPropertyInfo.RelativePosition.Transportations.Transportation.map(
                (t: any) => ({
                  TransportationCode: t.TransportationCode
                })
              )
            : {
                TransportationCode:
                  data.BasicPropertyInfo.RelativePosition.Transportations.Transportation
                    .TransportationCode
              }
        }
      }
    }
  })

  return Array.isArray(hotelStaysData.HotelStay)
    ? hotelStaysData.HotelStay.map(mapHotelStay)
    : [mapHotelStay(hotelStaysData.HotelStay)]
}

// Function to parse RoomStay
async function parseRoomStays(roomStaysData: any): Promise<RoomStay[]> {
  const mapRoomStay = (roomStayData: any): RoomStay => ({
    MarketCode: roomStayData.MarketCode,
    AvailabilityStatus: roomStayData.AvailabilityStatus,
    InfoSource: roomStayData.InfoSource,
    RPH: roomStayData.RPH,
    RatePlans: {
      RatePlan: {
        RatePlanCode: roomStayData.RatePlans.RatePlan.RatePlanCode,
        RateIndicator: roomStayData.RatePlans.RatePlan.RateIndicator,
        RatePlanName: roomStayData.RatePlans.RatePlan.RatePlanName,
        AvailabilityStatus: roomStayData.RatePlans.RatePlan.AvailabilityStatus,
        Guarantee: {
          HoldTime: roomStayData.RatePlans.RatePlan.Guarantee.HoldTime
        },
        RatePlanDescription: {
          Text: {
            Formatted: roomStayData.RatePlans.RatePlan.RatePlanDescription.Text.Formatted,
            Language: roomStayData.RatePlans.RatePlan.RatePlanDescription.Text.Language,
            $t: roomStayData.RatePlans.RatePlan.RatePlanDescription.Text._
          }
        },
        Commission: {
          StatusType: roomStayData.RatePlans.RatePlan.Commission.StatusType
        },
        MealsIncluded: {
          Breakfast: roomStayData.RatePlans.RatePlan.MealsIncluded.Breakfast,
          MealPlanIndicator: roomStayData.RatePlans.RatePlan.MealsIncluded.MealPlanIndicator
        }
      }
    },
    RoomRates: {
      RoomRate: {
        BookingCode: roomStayData.RoomRates.RoomRate.BookingCode,
        RoomTypeCode: roomStayData.RoomRates.RoomRate.RoomTypeCode,
        NumberOfUnits: roomStayData.RoomRates.RoomRate.NumberOfUnits,
        RatePlanCode: roomStayData.RoomRates.RoomRate.RatePlanCode,
        AvailabilityStatus: roomStayData.RoomRates.RoomRate.AvailabilityStatus,
        Rates: {
          Rate: Array.isArray(roomStayData.RoomRates.RoomRate.Rates.Rate)
            ? roomStayData.RoomRates.RoomRate.Rates.Rate.map((r: any) => ({
                EffectiveDate: r.EffectiveDate,
                ExpireDate: r.ExpireDate,
                RateTimeUnit: r.RateTimeUnit,
                Base: r.Base
                  ? { AmountAfterTax: r.Base.AmountAfterTax, CurrencyCode: r.Base.CurrencyCode }
                  : undefined,
                PaymentPolicies: r.PaymentPolicies
                  ? { GuaranteePayment: { HoldTime: r.PaymentPolicies.GuaranteePayment.HoldTime } }
                  : undefined,
                Total: r.Total
                  ? { AmountAfterTax: r.Total.AmountAfterTax, CurrencyCode: r.Total.CurrencyCode }
                  : undefined
              }))
            : [
                {
                  EffectiveDate: roomStayData.RoomRates.RoomRate.Rates.Rate.EffectiveDate,
                  ExpireDate: roomStayData.RoomRates.RoomRate.Rates.Rate.ExpireDate,
                  RateTimeUnit: roomStayData.RoomRates.RoomRate.Rates.Rate.RateTimeUnit,
                  Base: {
                    AmountAfterTax: roomStayData.RoomRates.RoomRate.Rates.Rate.Base.AmountAfterTax,
                    CurrencyCode: roomStayData.RoomRates.RoomRate.Rates.Rate.Base.CurrencyCode
                  },
                  PaymentPolicies: {
                    GuaranteePayment: {
                      HoldTime:
                        roomStayData.RoomRates.RoomRate.Rates.Rate.PaymentPolicies.GuaranteePayment
                          .HoldTime
                    }
                  }
                },
                {
                  Total: {
                    AmountAfterTax: roomStayData.RoomRates.RoomRate.Rates.Rate.Total.AmountAfterTax,
                    CurrencyCode: roomStayData.RoomRates.RoomRate.Rates.Rate.Total.CurrencyCode
                  }
                }
              ]
        },
        RoomRateDescription: {
          Name: roomStayData.RoomRates.RoomRate.RoomRateDescription.Name,
          CreatorID: roomStayData.RoomRates.RoomRate.RoomRateDescription.CreatorID,
          Text: Array.isArray(roomStayData.RoomRates.RoomRate.RoomRateDescription.Text)
            ? roomStayData.RoomRates.RoomRate.RoomRateDescription.Text.map((t: any) => ({
                Formatted: t.Formatted,
                Language: t.Language,
                $t: t._
              }))
            : [
                {
                  Formatted: roomStayData.RoomRates.RoomRate.RoomRateDescription.Text.Formatted,
                  Language: roomStayData.RoomRates.RoomRate.RoomRateDescription.Text.Language,
                  $t: roomStayData.RoomRates.RoomRate.RoomRateDescription.Text._
                }
              ]
        },
        Total: {
          AmountAfterTax: roomStayData.RoomRates.RoomRate.Total.AmountAfterTax,
          CurrencyCode: roomStayData.RoomRates.RoomRate.Total.CurrencyCode
        }
      }
    },
    GuestCounts: {
      GuestCount: Array.isArray(roomStayData.GuestCounts.GuestCount)
        ? roomStayData.GuestCounts.GuestCount.map((g: any) => ({
            AgeQualifyingCode: g.AgeQualifyingCode,
            Count: g.Count,
            Age: g.Age
          }))
        : [
            {
              AgeQualifyingCode: roomStayData.GuestCounts.GuestCount.AgeQualifyingCode,
              Count: roomStayData.GuestCounts.GuestCount.Count,
              Age: roomStayData.GuestCounts.GuestCount.Age
            }
          ]
    },
    TimeSpan: {
      Start: roomStayData.TimeSpan.Start,
      End: roomStayData.TimeSpan.End,
      StartDateWindow: {
        DOW: roomStayData.TimeSpan.StartDateWindow.DOW
      },
      EndDateWindow: {
        DOW: roomStayData.TimeSpan.EndDateWindow.DOW
      }
    },
    Total: {
      AmountAfterTax: roomStayData.Total.AmountAfterTax,
      CurrencyCode: roomStayData.Total.CurrencyCode
    },
    ServiceRPHs: {
      ServiceRPH: Array.isArray(roomStayData.ServiceRPHs.ServiceRPH)
        ? roomStayData.ServiceRPHs.ServiceRPH.map((s: any) => ({
            RPH: s.RPH
          }))
        : [{ RPH: roomStayData.ServiceRPHs.ServiceRPH.RPH }]
    }
  })

  return Array.isArray(roomStaysData.RoomStay)
    ? roomStaysData.RoomStay.map(mapRoomStay)
    : [mapRoomStay(roomStaysData.RoomStay)]
}

// Function to parse Services
async function parseServices(servicesData: any): Promise<Service[]> {
  const mapService = (serviceData: any): Service => ({
    ServicePricingType: serviceData.ServicePricingType,
    ServiceRPH: serviceData.ServiceRPH,
    ServiceInventoryCode: serviceData.ServiceInventoryCode,
    Inclusive: serviceData.Inclusive,
    Quantity: serviceData.Quantity,
    Type: serviceData.Type,
    ID: serviceData.ID,
    Price: serviceData.Price
      ? {
          EffectiveDate: serviceData.Price.EffectiveDate,
          ExpireDate: serviceData.Price.ExpireDate,
          RateTimeUnit: serviceData.Price.RateTimeUnit,
          UnitMultiplier: serviceData.Price.UnitMultiplier,
          Base: {
            AmountAfterTax: serviceData.Price.Base.AmountAfterTax,
            CurrencyCode: serviceData.Price.Base.CurrencyCode
          }
        }
      : undefined,
    ServiceDetails: {
      Comments: {
        Comment: Array.isArray(serviceData.ServiceDetails.Comments.Comment)
          ? serviceData.ServiceDetails.Comments.Comment.map((c: any) => ({
              Name: c.Name
            }))
          : { Name: serviceData.ServiceDetails.Comments.Comment.Name }
      },
      Total: serviceData.ServiceDetails.Total
        ? {
            AmountAfterTax: serviceData.ServiceDetails.Total.AmountAfterTax,
            CurrencyCode: serviceData.ServiceDetails.Total.CurrencyCode
          }
        : undefined
    }
  })

  return Array.isArray(servicesData.Service)
    ? servicesData.Service.map(mapService)
    : [mapService(servicesData.Service)]
}

// Main function to parse the SOAP XML into OTAHotelAvailRS
async function parseOtaHotelAvailRS(xmlString: string): Promise<OTAHotelAvailRS> {
  try {
    // Parse XML string to JavaScript object
    const result = await parseStringPromise(xmlString, {
      trim: true,
      explicitArray: false,
      mergeAttrs: true,
      normalizeTags: true
    })

    // Navigate SOAP structure
    const otaData = result['soap:envelope']['soap:body'].ota_hotelavailrs

    // Parse each section
    // const hotelStays = otaData.hotelstays ? await parseHotelStays(otaData.hotelstays.hotelstay) : []
    // const roomStays = otaData.roomstays ? await parseRoomStays(otaData.roomstays.roomstay) : []
    // const services = otaData.services ? await parseServices(otaData.services.service) : []

    return {
      HotelStays: otaData.hotelstays.hotelstay,
      RoomStays: otaData.roomstays,
      Services: otaData.services.service
    }
  } catch (error) {
    throw new Error(`Failed to parse XML: ${(error as Error).message}`)
  }
}

// Example usage
async function example() {
  const xmlString = `
    <?xml version="1.0" encoding="UTF-8"?>
    <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:awsse="http://xml.amadeus.com/2010/06/Session_v3" xmlns:wsa="http://www.w3.org/2005/08/addressing">
      <soap:Header></soap:Header>
      <soap:Body>
        <OTA_HotelAvailRS xmlns="http://www.opentravel.org/OTA/2003/05" EchoToken="MultiSingle" Version="6.001" PrimaryLangID="EN">
          <Success/>
          <Warnings>
            <Warning Type="3" Status="PRV.50" Tag="CLS"/>
            <Warning Type="3" Status="PRV.2" Tag="OK"/>
            <Warning Type="3" Status="PRV.12" Tag="PE"/>
            <Warning Type="3" Status="PRV.32" Tag="PUE"/>
          </Warnings>
          <HotelStays>
            <HotelStay RoomStayRPH="0 1">
              <BasicPropertyInfo ChainCode="HN" HotelCode="HNMADAK2" HotelCityCode="MAD" HotelName="CHILDREN RATES VALIDATION2" HotelCodeContext="1A" ChainName="House Of Travel" AreaID="1" SupplierIntegrationLevel="3">
                <Position Latitude="4038904" Longitude="-341405"/>
                <Address>
                  <AddressLine>Vielen Dank für die Gastfreundschaft</AddressLine>
                  <CityName>MADRID</CityName>
                  <PostalCode>560043</PostalCode>
                  <CountryName Code="ES">SPAIN</CountryName>
                </Address>
                <RelativePosition>
                  <Transportations>
                    <Transportation TransportationCode="20"/>
                  </Transportations>
                </RelativePosition>
              </BasicPropertyInfo>
            </HotelStay>
          </HotelStays>
          <RoomStays MoreIndicator="TRQ4SPVGD58W_96">
            <RoomStay MarketCode="Flat" AvailabilityStatus="AvailableForSale" InfoSource="HN" RPH="0">
              <RatePlans>
                <RatePlan RatePlanCode="DSP" RateIndicator="AvailableForSale" RatePlanName="Mountain view rooms" AvailabilityStatus="AvailableForSale">
                  <Guarantee HoldTime="13:00:00"/>
                  <RatePlanDescription>
                    <Text Formatted="1" Language="EN">Rate Code: 3 for April month. Room only for 1 night</Text>
                  </RatePlanDescription>
                  <Commission StatusType="Commissionable"/>
                  <MealsIncluded Breakfast="0" MealPlanIndicator="0"/>
                </RatePlan>
              </RatePlans>
              <RoomRates>
                <RoomRate BookingCode="001000A" RoomTypeCode="A1D" NumberOfUnits="1" RatePlanCode="DSP" AvailabilityStatus="AvailableForSale">
                  <Rates>
                    <Rate EffectiveDate="2025-05-08" ExpireDate="2025-05-09" RateTimeUnit="Day">
                      <Base AmountAfterTax="32.00" CurrencyCode="EUR"/>
                      <PaymentPolicies>
                        <GuaranteePayment HoldTime="13:00:00"/>
                      </PaymentPolicies>
                    </Rate>
                    <Rate>
                      <Total AmountAfterTax="32.00" CurrencyCode="EUR"/>
                    </Rate>
                  </Rates>
                  <RoomRateDescription Name="KUKAHI AINA VIEW DOUBLE ROOM" CreatorID="1">
                    <Text Formatted="1" Language="EN">Rate Code: 3 for April month. Room only for 1 night</Text>
                    <Text Formatted="1" Language="EN">KUKAHI AINA VIEW DOUBLE ROOM</Text>
                  </RoomRateDescription>
                  <Total AmountAfterTax="32.00" CurrencyCode="EUR"/>
                </RoomRate>
              </RoomRates>
              <GuestCounts>
                <GuestCount AgeQualifyingCode="10" Count="2"/>
                <GuestCount AgeQualifyingCode="8" Age="6" Count="1"/>
              </GuestCounts>
              <TimeSpan Start="2025-05-08" End="2025-05-09">
                <StartDateWindow DOW="Thu"/>
                <EndDateWindow DOW="Fri"/>
              </TimeSpan>
              <Total AmountAfterTax="32.00" CurrencyCode="EUR"/>
              <ServiceRPHs>
                <ServiceRPH RPH="0"/>
                <ServiceRPH RPH="1"/>
                <ServiceRPH RPH="2"/>
                <ServiceRPH RPH="3"/>
              </ServiceRPHs>
            </RoomStay>
          </RoomStays>
          <Services>
            <Service ServicePricingType="Per person" ServiceRPH="0" ServiceInventoryCode="47.HAC" Inclusive="1" Quantity="1" Type="10" ID="3MP">
              <Price EffectiveDate="2025-05-08" ExpireDate="2025-05-08" RateTimeUnit="Day" UnitMultiplier="1">
                <Base AmountAfterTax="30.00" CurrencyCode="EUR"/>
              </Price>
              <ServiceDetails>
                <Comments>
                  <Comment Name="Special breakfast"/>
                </Comments>
                <Total AmountAfterTax="90.00" CurrencyCode="EUR"/>
              </ServiceDetails>
            </Service>
            <Service ServicePricingType="Per stay" ServiceRPH="1" ServiceInventoryCode="1.HAC" Inclusive="0" Quantity="1" Type="10" ID="EB4">
              <Price EffectiveDate="2025-05-08" ExpireDate="2025-05-08" RateTimeUnit="FullDuration" UnitMultiplier="1">
                <Base AmountAfterTax="80.00" CurrencyCode="EUR"/>
              </Price>
              <ServiceDetails>
                <Comments>
                  <Comment Name="EXTRA DRINK"/>
                </Comments>
                <Total AmountAfterTax="80.00" CurrencyCode="EUR"/>
              </ServiceDetails>
            </Service>
            <Service ServicePricingType="Per person" ServiceRPH="2" ServiceInventoryCode="1.MPT" Inclusive="0" Quantity="1" Type="10" ID="1MP">
              <Price EffectiveDate="2025-05-08" ExpireDate="2025-05-08" RateTimeUnit="Day" UnitMultiplier="1">
                <Base AmountAfterTax="5.00" CurrencyCode="EUR"/>
              </Price>
              <ServiceDetails>
                <Comments>
                  <Comment Name="MEAL PLAN SERVICE"/>
                </Comments>
                <Total AmountAfterTax="15.00" CurrencyCode="EUR"/>
              </ServiceDetails>
            </Service>
            <Service ServicePricingType="Per stay" ServiceRPH="3" ServiceInventoryCode="77.HAC" Inclusive="0" Quantity="1" Type="10" ID="2MP">
              <Price EffectiveDate="2025-05-08" ExpireDate="2025-05-08" RateTimeUnit="FullDuration" UnitMultiplier="1">
                <Base AmountAfterTax="20.00" CurrencyCode="EUR"/>
              </Price>
              <ServiceDetails>
                <Comments>
                  <Comment Name="Special breakfast"/>
                </Comments>
                <Total AmountAfterTax="20.00" CurrencyCode="EUR"/>
              </ServiceDetails>
            </Service>
          </Services>
        </OTA_HotelAvailRS>
      </soap:Body>
    </soap:Envelope>
  `

  try {
    const otaData = await parseOtaHotelAvailRS(xmlString)
    console.log(JSON.stringify(otaData, null, 2))
  } catch (error) {
    console.error(error)
  }
}

export { parseOtaHotelAvailRS, OTAHotelAvailRS, HotelStay, RoomStay, Service }
