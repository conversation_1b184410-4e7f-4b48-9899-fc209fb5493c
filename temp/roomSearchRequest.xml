<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:add="http://www.w3.org/2005/08/addressing" xmlns:oas="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:oasl="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:sec="http://xml.amadeus.com/2010/06/Security_v1" xmlns:awsse="http://xml.amadeus.com/2010/06/Session_v3">
    <soap:Header>
        <add:MessageID>fabf32b0-0d50-4d82-82e0-5362c2eee2b2</add:MessageID>
        <add:Action xmlns:add="http://www.w3.org/2005/08/addressing">http://webservices.amadeus.com/Hotel_MultiSingleAvailability_10.0</add:Action>
        <add:To>https://noded5.test.webservices.amadeus.com/1ASIWVIELT4</add:To>
        <oas:Security>
            <oas:UsernameToken oasl:Id="UsernameToken-1">
                <oas:Username>WSLT4VIE</oas:Username>
                <oas:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">ufPd8Hd42LAeSO0A1nw3TQ==</oas:Nonce>
                <oas:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">NfKs26GkZQ5/z9GkeJ+Ij35VDP0=</oas:Password>
                <oasl:Created>2025-06-02T00:39:57.231Z</oasl:Created>
            </oas:UsernameToken>
        </oas:Security>
        <sec:AMA_SecurityHostedUser>
            <sec:UserID AgentDutyCode="SU" RequestorType="U" PseudoCityCode="TUL1S2400" POS_Type="1"/>
        </sec:AMA_SecurityHostedUser>
        <awsse:Session TransactionStatusCode="Start"/>
    </soap:Header>
    <soap:Body>
        <OTA_HotelAvailRQ SearchCacheLevel="Live" EchoToken="MultiSingle" SummaryOnly="true" RateRangeOnly="true" RateDetailsInd="true" AvailRatesOnly="true" RequestedCurrency="USD" Version="4.000" PrimaryLangID="EN">
            <AvailRequestSegments>
                <AvailRequestSegment InfoSource="Distribution">
                    <HotelSearchCriteria AvailableOnlyIndicator="true">
                        <Criterion>
                            <StayDateRange Start="2025-06-06" End="2025-06-08" />
                            <RoomStayCandidates>
                                <RoomStayCandidate RoomID="1" Quantity="1">
                                    <GuestCounts IsPerRoom="true">
  <GuestCount AgeQualifyingCode="10" Count="1"/>
  <GuestCount AgeQualifyingCode="8" Count="1" Age="6"/>
</GuestCounts>
                                </RoomStayCandidate>
                            </RoomStayCandidates>
                        </Criterion>
                        <Criterion ExactMatch="true">
                            <HotelRef HotelCode="RTMADCMP" />
                        </Criterion>
                    </HotelSearchCriteria>
                </AvailRequestSegment>
            </AvailRequestSegments>
        </OTA_HotelAvailRQ>
    </soap:Body>
</soap:Envelope>