import { Controller, Post, Body, Query, Get, HttpCode, HttpStatus } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBody } from '@nestjs/swagger'
import { HotelService } from './hotel.service'
import { GeneralSearchDto } from './dto/generalSearch.dto'
import { RoomPriceDto } from './dto/roomPrice.dto'
import { UserFillFormDto } from './dto/userFillForm.dto'
import { HotelSellDto } from './dto/hotellSell.dto'
import { SystemConfirmDto } from './dto/systemConfirm.dto'
import { PnrRetrieveDto } from './dto/pnrRetrieve.dto'
import { DescriptiveRequestDto } from './dto/descriptiveRequest.dto'
import { SystemConfirmResponseDto, ApiErrorDto } from './dto/systemConfirmResponse.dto'
import { PnrRetrieveResponseDto } from './dto/pnrRetrieveResponse.dto'
import { HotelSellResponseDto, HotelSellErrorDto } from './dto/hotelSellResponse.dto'

@Controller('hotel')
export class HotelController {
  constructor(private readonly hotelService: HotelService) {}

  @Get('search')
  async search(@Query('keyword') keyword: string) {
    return await this.hotelService.search(keyword)
  }

  @Post('general-search')
  async generalSearch(@Body() data: GeneralSearchDto) {
    return await this.hotelService.gdsGeneralSearch(data)
  }

  @Post('room-search')
  async roomSearch(@Body() data: GeneralSearchDto) {
    return await this.hotelService.gdsRoomSearch(data)
  }

  @Post('descriptive-info')
  async descriptiveInfo(@Query('hotelCode') hotelCode: string) {
    return await this.hotelService.gdsDescriptiveInfo([hotelCode])
  }

  @Post('room-price-detail')
  async roomPriceDetail(@Body() data: RoomPriceDto) {
    return await this.hotelService.gdsRoomPriceDetail(data)
  }

  @Post('fill-user-info')
  async fillUserInfo(@Body() data: UserFillFormDto) {
    return await this.hotelService.gdsUserFillForm(data)
  }

  @Post('hotel-sell')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Complete hotel booking',
    description: 'Finalize the hotel booking with payment information and complete the reservation'
  })
  @ApiBody({ type: HotelSellDto })
  @ApiResponse({
    status: 200,
    description: 'Hotel booking successfully completed',
    type: HotelSellResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request or invalid data',
    type: HotelSellErrorDto
  })
  @ApiResponse({
    status: 401,
    description: 'Session expired or invalid',
    type: ApiErrorDto
  })
  @ApiResponse({
    status: 409,
    description: 'Booking conflict or room unavailable',
    type: HotelSellErrorDto
  })
  @ApiResponse({
    status: 422,
    description: 'Booking processing error',
    type: HotelSellErrorDto
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async hotelSell(@Body() data: HotelSellDto) {
    return await this.hotelService.gdsSell(data)
  }

  @Post('system-confirm')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'System confirm PNR',
    description: 'Finalize and confirm the hotel booking PNR in the system'
  })
  @ApiBody({ type: SystemConfirmDto })
  @ApiResponse({
    status: 200,
    description: 'PNR successfully confirmed and saved',
    type: SystemConfirmResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request or session expired',
    type: ApiErrorDto
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async systemConfirm(@Body() data: SystemConfirmDto) {
    return await this.hotelService.gdsSystemConfirm(data)
  }

  
  @Post('pnr-retrieve')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Retrieve PNR by locator',
    description: 'Retrieve complete PNR information using PNR locator/control number'
  })
  @ApiBody({ type: PnrRetrieveDto })
  @ApiResponse({
    status: 200,
    description: 'PNR successfully retrieved',
    type: PnrRetrieveResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request or invalid PNR locator',
    type: ApiErrorDto
  })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async pnrRetrieve(@Body() data: PnrRetrieveDto) {
    return await this.hotelService.gdsPnrRetrieve(data)
  }

}
