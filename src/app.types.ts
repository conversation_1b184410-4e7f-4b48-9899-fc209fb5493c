export enum UserType {
  PREORDER = 'PREORDER',
  USER = 'USER'
}

export enum ChallengeType {
  FIRST_TIME = 1,
  DAILY = 2
}

export enum DataType {
  STEPS = 1,
  SWIMMING = 2,
  SLEEP = 3,
  RUNNING = 4,
  HIKING = 5,
  CYCLING = 6
}

export enum DataSource {
  APPLE_HEALTH = 'apple_health',
  GOOGLE_FIT = 'google_fit',
  RING = 'ring',
  ANDROID = 'android',
  IOS = 'ios'
}

export enum DataUnit {
  STEPS = 'count',
  SWIMMING = 'meters'
}

export enum PointType {
  SYNC = 'sync',
  CHALLENGE = 'challenge',
  REFERRAL = 'referral'
}

export enum ReferralType {
  AMBASSADOR = 1,
  REFERRAL = 2
}

export enum ChallengeIdType {
  REGISTER = 1,
  SYNC_DATA = 2,
  STEPS = 3,
  DAILY = 4,
  INTIME = 5
}

export enum VerifyCodeType {
  VERIFY_EMAIL = 1,
  RESET_PASSWORD = 2
}

export enum DeviceType {
  PHONE = 'phone',
  RING = 'ring'
}

export enum SpinRewardType {
  SPIN = 'spin',
  POINT = 'point',
  NOTHING = 'nothing',
  RING = 'ring',
  TRUMP_NFT = 'trump_nft'
}

export enum TransactionType {
  CONVERT = 'convert',
  BURN = 'burn',
  MINT = 'mint',
  TRANSFER = 'transfer'
}

export enum ConversionType {
  POINT = 'POINT',
  COMMISSION = 'COMMISSION'
}

export enum ConversionStatus {
  PENDING = 'PENDING',
  SIGNED = 'SIGNED',
  FAILED = 'FAILED',
  CLAIMED = 'CLAIMED'
}
