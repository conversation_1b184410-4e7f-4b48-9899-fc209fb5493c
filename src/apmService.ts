// initapm.ts
import * as apm from 'elastic-apm-node'
import { APM_SECRET_TOKEN, APM_SERVER, NODE_ENV, ELASTIC_SERVICE_NAME } from './app.settings'
// Start Elastic APM
console.log('APM_SERVER:', apm)
try {
  apm.start({
    // Required: Name of your service
    serviceName: ELASTIC_SERVICE_NAME,
    // APM Server URL (default is http://localhost:8200 if you’re running Elastic locally)

    serverUrl: APM_SERVER,
    // Optional: Set environment (e.g., 'development', 'production')
    environment: NODE_ENV,
    // Optional: Add your Elastic APM secret token if required
    secretToken: APM_SECRET_TOKEN,
    instrument: true,
    transactionSampleRate: 1.0
  })
} catch (error) {
  console.error('Error starting APM:', error)
}
