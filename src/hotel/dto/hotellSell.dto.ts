import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsNotEmpty, IsString, IsBoolean, IsOptional, <PERSON><PERSON>ength, <PERSON><PERSON>ength, IsNumberString, Length, Matches } from 'class-validator'
export class HotelSellDto {
  @ApiProperty({
    description: 'The session id from previous search',
    example: '02JV3Q93R0'
  })
  @IsNotEmpty()
  @IsString()
  sessionId: string

  @ApiProperty({
    description: 'The sequence number from previous search',
    example: '5'
  })
  @IsNotEmpty()
  @IsString()
  @IsNumberString()
  sequenceNumber: string

  @ApiProperty({
    description: 'The security token from previous search',
    example: '1YVE4IWPFJ8NQ3MECH1J0L8KH0'
  })
  @IsNotEmpty()
  @IsString()
  securityToken: string

  @ApiProperty({
    description: 'The chain code of the hotel',
    example: 'RT'
  })
  @IsNotEmpty()
  @IsString()
  @Length(1, 10)
  chainCode: string

  @ApiProperty({
    description: 'The hotel city code',
    example: 'MAD'
  })
  @IsNotEmpty()
  @IsString()
  @Length(2, 5)
  hotelCityCode: string

  @ApiProperty({
    description: 'The hotel code',
    example: 'CMP'
  })
  @IsNotEmpty()
  @IsString()
  @Length(3, 3)
  hotelCode: string

  @ApiProperty({
    description: 'The guest tattoo',
    example: '2'
  })
  @IsNotEmpty()
  @IsString()
  @IsNumberString()
  guestTattoo: string

  @ApiProperty({
    description: 'The booking code',
    example: 'B1Q1SL'
  })
  @IsNotEmpty()
  @IsString()
  @Length(1, 10)
  bookingCode: string

  @ApiProperty({
    description: 'The credit card vendor code',
    example: 'VI'
  })
  @IsNotEmpty()
  @IsString()
  @Length(2, 4)
  CC_VENDOR_CODE: string

  @ApiProperty({
    description: 'The credit card number',
    example: '****************'
  })
  @IsNotEmpty()
  @IsString()
  @Length(13, 19)
  @Matches(/^[0-9]+$/, { message: 'Credit card number must contain only digits' })
  CC_NUMBER: string

  @ApiProperty({
    description: 'The credit card security id',
    example: '999'
  })
  @IsNotEmpty()
  @IsString()
  @Length(3, 4)
  @Matches(/^[0-9]+$/, { message: 'Security code must contain only digits' })
  CC_SECURITY_ID: string

  @ApiProperty({
    description: 'The credit card expiry date',
    example: '1227'
  })
  @IsNotEmpty()
  @IsString()
  @Length(4, 4)
  @Matches(/^(0[1-9]|1[0-2])\d{2}$/, { message: 'Expiry date must be in format MMYY' })
  CC_EXPIRY_DATE: string
}
