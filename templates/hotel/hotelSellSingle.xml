<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ses="http://xml.amadeus.com/2010/06/Session_v3">
    <soap:Header>
        <ses:Session TransactionStatusCode="InSeries">
            <ses:SessionId>{{sessionId}}</ses:SessionId>
            <ses:SequenceNumber>{{sequenceNumber}}</ses:SequenceNumber>
            <ses:SecurityToken>{{securityToken}}</ses:SecurityToken>
        </ses:Session>
        <add:MessageID xmlns:add="http://www.w3.org/2005/08/addressing">{{messageId}}</add:MessageID>
        <add:Action xmlns:add="http://www.w3.org/2005/08/addressing">{{actionUrl}}</add:Action>
        <add:To xmlns:add="http://www.w3.org/2005/08/addressing">{{apiUrl}}</add:To>
        <link:TransactionFlowLink xmlns:link="http://wsdl.amadeus.com/2010/06/ws/Link_v1" />
    </soap:Header>
    <soap:Body>
        <Hotel_Sell>
            <roomStayData>
                <markerRoomStayData />
                <globalBookingInfo>
                    <markerGlobalBookingInfo>
                        <hotelReference>
                            <chainCode>{{chainCode}}</chainCode>
                            <cityCode>{{hotelCityCode}}</cityCode>
                            <hotelCode>{{hotelCode}}</hotelCode>
                        </hotelReference>
                    </markerGlobalBookingInfo>
                    <representativeParties>
                        <occupantList>
                            <passengerReference>
                                <type>BHO</type>
                                <value>{{guestTattoo}}</value>
                            </passengerReference>
                        </occupantList>
                    </representativeParties>
                </globalBookingInfo>
                <roomList>
                    <markerRoomstayQuery />
                    <roomRateDetails>
                        <marker />
                        <hotelProductReference>
                            <referenceDetails>
                                <type>BC</type>
                                <value>{{bookingCode}}</value>
                            </referenceDetails>
                        </hotelProductReference>
                        <markerOfExtra />
                    </roomRateDetails>
                    <guaranteeOrDeposit>
                        <paymentInfo>
                            <paymentDetails>
                                <formOfPaymentCode>1</formOfPaymentCode>
                                <paymentType>2</paymentType>
                                <serviceToPay>3</serviceToPay>
                            </paymentDetails>
                        </paymentInfo>
                        <groupCreditCardInfo>
                            <creditCardInfo>
                                <ccInfo>
                                    <vendorCode>{{CC_VENDOR_CODE}}</vendorCode>
                                    <cardNumber>{{CC_NUMBER}}</cardNumber>
                                    <securityId>{{CC_SECURITY_ID}}</securityId>
                                    <expiryDate>{{CC_EXPIRY_DATE}}</expiryDate>
                                </ccInfo>
                            </creditCardInfo>
                        </groupCreditCardInfo>
                    </guaranteeOrDeposit>
                </roomList>
            </roomStayData>
        </Hotel_Sell>
    </soap:Body>
</soap:Envelope>
