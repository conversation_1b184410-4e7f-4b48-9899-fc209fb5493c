<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ses="http://xml.amadeus.com/2010/06/Session_v3">
    <soap:Header>
        <ses:Session TransactionStatusCode="InSeries">
            <ses:SessionId>{{sessionId}}</ses:SessionId>
            <ses:SequenceNumber>{{sequenceNumber}}</ses:SequenceNumber>
            <ses:SecurityToken>{{securityToken}}</ses:SecurityToken>
        </ses:Session>
        <add:MessageID xmlns:add="http://www.w3.org/2005/08/addressing">{{messageId}}</add:MessageID>
        <add:Action xmlns:add="http://www.w3.org/2005/08/addressing">{{actionUrl}}</add:Action>
        <add:To xmlns:add="http://www.w3.org/2005/08/addressing">{{apiUrl}}</add:To>
        <link:TransactionFlowLink xmlns:link="http://wsdl.amadeus.com/2010/06/ws/Link_v1" />
    </soap:Header>
    <soap:Body>
        <OTA_HotelAvailRQ EchoToken="Pricing" Version="4.000" PrimaryLangID="EN" SummaryOnly="false" AvailRatesOnly="true" OnRequestInd="true" RateDetailsInd="true" SearchCacheLevel="Live" RequestedCurrency="EUR" ExactMatchOnly="true" RateRangeOnly="false">
            <AvailRequestSegments>
                <AvailRequestSegment InfoSource="Distribution">
                    <HotelSearchCriteria>
                        <Criterion ExactMatch="true">
                            <HotelRef HotelCodeContext="1A" ChainCode="{{chainCode}}" HotelCityCode="{{hotelCityCode}}" HotelCode="{{hotelCode}}" />
                            <StayDateRange Start="{{checkinDate}}" End="{{checkoutDate}}" />
                            <RatePlanCandidates>
                                <RatePlanCandidate RatePlanCode="{{ratePlanCode}}"></RatePlanCandidate>
                            </RatePlanCandidates>
                            <RoomStayCandidates>
                                <RoomStayCandidate RoomTypeCode="{{roomTypeCode}}" RoomID="1" Quantity="{{roomQuantity}}" BookingCode="{{bookingCode}}">
                                    {{guestCountDetails}}
                                </RoomStayCandidate>
                            </RoomStayCandidates>
                        </Criterion>
                    </HotelSearchCriteria>
                </AvailRequestSegment>
            </AvailRequestSegments>
        </OTA_HotelAvailRQ>
    </soap:Body>
</soap:Envelope>