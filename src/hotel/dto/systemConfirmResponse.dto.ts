import { ApiProperty } from '@nestjs/swagger'

export class PnrInfoDto {
  @ApiProperty({
    description: 'Company ID',
    example: 'RT'
  })
  companyId: string

  @ApiProperty({
    description: 'PNR control number',
    example: '3753ZF3500'
  })
  controlNumber: string

  @ApiProperty({
    description: 'Creation date',
    example: '241220'
  })
  date: string

  @ApiProperty({
    description: 'Creation time',
    example: '1030'
  })
  time: string
}

export class PnrDetailsDto {
  @ApiProperty({
    description: 'PNR information',
    type: PnrInfoDto
  })
  pnrInfo: PnrInfoDto

  @ApiProperty({
    description: 'PNR status',
    example: 'CONFIRMED'
  })
  status: string

  @ApiProperty({
    description: 'Success message',
    example: 'PNR has been successfully confirmed and saved'
  })
  message: string
}

export class SystemConfirmResponseDto {
  @ApiProperty({
    description: 'Session ID',
    example: '03KYBGFIL5'
  })
  sessionId: string

  @ApiProperty({
    description: 'Sequence number',
    example: '5'
  })
  sequenceNumber: string

  @ApiProperty({
    description: 'Security token',
    example: '1O1L6FKTCRO63QD6NCG6VH2EE'
  })
  securityToken: string

  @ApiProperty({
    description: 'PNR details',
    type: PnrDetailsDto
  })
  pnrDetails: PnrDetailsDto
}

export class ApiErrorDto {
  @ApiProperty({
    description: 'Error type',
    example: 'API_ERROR'
  })
  error: string

  @ApiProperty({
    description: 'Error message',
    example: 'Session has expired or invalid session data'
  })
  message: string

  @ApiProperty({
    description: 'Additional error details',
    required: false
  })
  details?: any
}
