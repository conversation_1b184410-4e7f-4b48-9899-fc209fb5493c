{"soap:envelope": {"xmlns:soap": "http://schemas.xmlsoap.org/soap/envelope/", "xmlns:awsse": "http://xml.amadeus.com/2010/06/Session_v3", "xmlns:wsa": "http://www.w3.org/2005/08/addressing", "soap:header": {"wsa:to": "http://www.w3.org/2005/08/addressing/anonymous", "wsa:from": {"wsa:address": "https://noded5.test.webservices.amadeus.com/1ASIWVIELT4"}, "wsa:action": "http://webservices.amadeus.com/PNRADD_22_1_1A", "wsa:messageid": "urn:uuid:524419cf-226f-fca4-9902-99f4924d6a9c", "wsa:relatesto": {"_": "55f3a78d-**************-dfa073f980cd", "RelationshipType": "http://www.w3.org/2005/08/addressing/reply"}, "awsse:session": {"TransactionStatusCode": "InSeries", "awsse:sessionid": "00DTTG7SC0", "awsse:sequencenumber": "4", "awsse:securitytoken": "15GNKWHJBBABC1AZLQFPNOT47X"}}, "soap:body": {"pnr_reply": {"xmlns": "http://xml.amadeus.com/PNRACC_22_1_1A", "pnrheader": {"reservationinfo": {"reservation": {"companyid": "1A"}}}, "securityinformation": {"responsibilityinformation": {"typeofpnrelement": "RP", "officeid": "TUL1S2400", "iatacode": "00886082"}, "queueinginformation": {"queueingofficeid": "TUL1S2400"}, "citycode": "TUL"}, "sbrposdetails": {"sbruseridentificationown": {"originidentification": {"inhouseidentification1": " "}}, "sbrsystemdetails": {"deliveringsystem": {"companyid": " "}}, "sbrpreferences": {"userpreferences": {"codedcountry": " "}}}, "sbrcreationposdetails": {"sbruseridentificationown": {"originidentification": {"inhouseidentification1": " "}}, "sbrsystemdetails": {"deliveringsystem": {"companyid": " "}}, "sbrpreferences": {"userpreferences": {"codedcountry": " "}}}, "sbrupdatorposdetails": {"sbruseridentificationown": {"originidentification": {"originatorid": "00886082", "inhouseidentification1": "TUL1S2400"}, "originatortypecode": "N"}, "sbrsystemdetails": {"deliveringsystem": {"companyid": "1A", "locationid": "TUL"}}, "sbrpreferences": {"userpreferences": {"codedcountry": "US"}}}, "travellerinfo": {"elementmanagementpassenger": {"reference": {"qualifier": "PT", "number": "2"}, "segmentname": "NM", "linenumber": "1"}, "passengerdata": {"travellerinformation": {"traveller": {"surname": "NGUYEN", "quantity": "1"}, "passenger": {"firstname": "VAN A"}}}, "enhancedpassengerdata": {"enhancedtravellerinformation": {"travellernameinfo": {"quantity": "1"}, "otherpaxnamesdetails": {"nametype": "UN", "referencename": "Y", "displayedname": "Y", "surname": "NGUYEN", "givenname": "VAN A"}}}}, "dataelementsmaster": {"marker2": "", "dataelementsindiv": [{"elementmanagementdata": {"segmentname": "RF"}, "otherdatafreetext": {"freetextdetail": {"subjectqualifier": "3", "type": "P22"}, "longfreetext": "AGENTNAME-NMC-US/WSLT4VIE"}}, {"elementmanagementdata": {"reference": {"qualifier": "OT", "number": "1"}, "segmentname": "AP", "linenumber": "2"}, "otherdatafreetext": {"freetextdetail": {"subjectqualifier": "3", "type": "5"}, "longfreetext": "+84912345678"}, "referencefordataelement": {"reference": {"qualifier": "PT", "number": "2"}}}, {"elementmanagementdata": {"reference": {"qualifier": "OT", "number": "2"}, "segmentname": "AP", "linenumber": "3"}, "otherdatafreetext": {"freetextdetail": {"subjectqualifier": "3", "type": "P02"}, "longfreetext": "<EMAIL>"}, "referencefordataelement": {"reference": {"qualifier": "PT", "number": "2"}}}, {"elementmanagementdata": {"reference": {"qualifier": "OT", "number": "4"}, "segmentname": "TK", "linenumber": "4"}, "ticketelement": {"ticket": {"indicator": "OK", "date": "270525", "officeid": "TUL1S2400"}}}, {"elementmanagementdata": {"reference": {"qualifier": "OT", "number": "3"}, "segmentname": "OS", "linenumber": "5"}, "otherdatafreetext": {"freetextdetail": {"subjectqualifier": "3", "type": "28", "companyid": "YY"}, "longfreetext": "HIGH FLOOR REQUESTED"}, "referencefordataelement": {"reference": {"qualifier": "PT", "number": "2"}}}]}}}}}