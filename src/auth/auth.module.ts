import { Modu<PERSON> } from '@nestjs/common'
import { AuthController } from './auth.controller'
import { AuthService } from './auth.service'
import { JwtModule } from '@nestjs/jwt'
import { JWT_EXP, JWT_SECRET } from '../app.settings'
import { PrismaService } from '../prisma.service'
import { PassportModule } from '@nestjs/passport'
import { JwtStrategy } from './jwt.strategy'
import { MailModule } from '../mail/mail.module'
@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: JWT_SECRET,
      signOptions: {
        algorithm: 'HS512',
        expiresIn: JWT_EXP
      }
    }),
    MailModule
  ],
  controllers: [AuthController],
  providers: [AuthService, PrismaService, JwtStrategy],
  exports: [AuthService]
})
export class AuthModule {}
