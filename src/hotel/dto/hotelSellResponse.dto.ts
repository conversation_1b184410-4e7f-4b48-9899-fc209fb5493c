import { ApiProperty } from '@nestjs/swagger'

export class BookingStatusDto {
  @ApiProperty({
    description: 'Number of rooms booked',
    example: '1'
  })
  quantity: string

  @ApiProperty({
    description: 'Booking status code',
    example: 'HK'
  })
  statusCode: string
}

export class PnrReferenceDto {
  @ApiProperty({
    description: 'Reference type',
    example: 'S'
  })
  type: string

  @ApiProperty({
    description: 'Reference value',
    example: '1'
  })
  value: string
}

export class HotelInfoDto {
  @ApiProperty({
    description: 'Hotel chain code',
    example: 'RT'
  })
  chainCode: string

  @ApiProperty({
    description: 'City code',
    example: 'MAD'
  })
  cityCode: string

  @ApiProperty({
    description: 'Hotel code',
    example: 'IIS'
  })
  hotelCode: string

  @ApiProperty({
    description: 'Hotel name',
    example: 'IBIS MADRID AEROPUERTO'
  })
  hotelName: string
}

export class ReservationDto {
  @ApiProperty({
    description: 'Company ID',
    example: 'RT'
  })
  companyId: string

  @ApiProperty({
    description: 'Control number',
    example: '3753ZF3500'
  })
  controlNumber: string

  @ApiProperty({
    description: 'Control type',
    example: '2'
  })
  controlType: string
}

export class PriceInfoDto {
  @ApiProperty({
    description: 'Room rate amount',
    example: '43.33'
  })
  amount: string

  @ApiProperty({
    description: 'Currency code',
    example: 'EUR'
  })
  currency: string

  @ApiProperty({
    description: 'Total amount',
    example: '72.00'
  })
  totalAmount: string
}

export class CreditCardDto {
  @ApiProperty({
    description: 'Credit card vendor code',
    example: 'VI'
  })
  vendorCode: string

  @ApiProperty({
    description: 'Masked credit card number',
    example: '4111****1116'
  })
  number: string

  @ApiProperty({
    description: 'Masked security code',
    example: '***'
  })
  securityId: string

  @ApiProperty({
    description: 'Expiry date',
    example: '1227'
  })
  expiryDate: string
}

export class RequestDataDto {
  @ApiProperty({
    description: 'Session ID used in request',
    example: '02JV3Q93R0'
  })
  sessionId: string

  @ApiProperty({
    description: 'Sequence number used in request',
    example: '3'
  })
  sequenceNumber: string

  @ApiProperty({
    description: 'Security token used in request',
    example: '1YVE4IWPFJ8NQ3MECH1J0L8KH0'
  })
  securityToken: string

  @ApiProperty({
    description: 'Credit card information (masked)',
    type: CreditCardDto
  })
  creditCard: CreditCardDto
}

export class BookingDetailsDto {
  @ApiProperty({
    description: 'Booking status',
    type: BookingStatusDto
  })
  status: BookingStatusDto

  @ApiProperty({
    description: 'PNR reference',
    type: PnrReferenceDto
  })
  pnrReference: PnrReferenceDto

  @ApiProperty({
    description: 'Hotel information',
    type: HotelInfoDto
  })
  hotelInfo: HotelInfoDto

  @ApiProperty({
    description: 'Reservation details',
    type: ReservationDto
  })
  reservation: ReservationDto

  @ApiProperty({
    description: 'Price information',
    type: PriceInfoDto
  })
  priceInfo: PriceInfoDto
}

export class HotelSellResponseDto {
  @ApiProperty({
    description: 'New session ID',
    example: '02JV3Q93R0'
  })
  sessionId: string

  @ApiProperty({
    description: 'New sequence number',
    example: '4'
  })
  sequenceNumber: string

  @ApiProperty({
    description: 'New security token',
    example: '1YVE4IWPFJ8NQ3MECH1J0L8KH0'
  })
  securityToken: string

  @ApiProperty({
    description: 'Booking details',
    type: BookingDetailsDto
  })
  bookingDetails: BookingDetailsDto

  @ApiProperty({
    description: 'Request data used',
    type: RequestDataDto
  })
  requestData: RequestDataDto

  @ApiProperty({
    description: 'Success message',
    example: 'Hotel booking has been successfully processed'
  })
  message: string
}

export class HotelSellErrorDto {
  @ApiProperty({
    description: 'Error type',
    enum: ['INVALID_VALUE_ERROR', 'PAYMENT_ERROR', 'SESSION_ERROR', 'AVAILABILITY_ERROR', 'BOOKING_ERROR'],
    example: 'INVALID_VALUE_ERROR'
  })
  error: string

  @ApiProperty({
    description: 'User-friendly error message',
    example: 'Invalid value found in the request. Please check your booking details, credit card information, or session data.'
  })
  message: string

  @ApiProperty({
    description: 'Error details',
    type: 'object'
  })
  details: any

  @ApiProperty({
    description: 'Suggestions to fix the error',
    type: [String],
    example: [
      'Check that all booking parameters are correct and properly formatted',
      'Verify credit card number, expiry date (MMYY format), and security code'
    ]
  })
  suggestions: string[]
}
