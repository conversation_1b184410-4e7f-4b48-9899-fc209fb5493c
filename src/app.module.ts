import { Module } from '@nestjs/common'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { ConfigService } from '@nestjs/config'
import { AuthModule } from './auth/auth.module'
import { ServeStaticModule } from '@nestjs/serve-static'
import { join } from 'path'
import { BullModule } from '@nestjs/bullmq'
import { REDIS_HOST, REDIS_PASSWORD, REDIS_PORT, REDIS_USER } from './app.settings'
import { HotelModule } from './hotel/hotel.module'

@Module({
  imports: [
    AuthModule,
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
      serveRoot: '/public'
    }),
    BullModule.forRoot({
      connection: {
        host: REDIS_HOST,
        port: Number(REDIS_PORT),
        username: REDIS_USER,
        password: REDIS_PASSWORD
      }
    }),
    HotelModule
  ],
  controllers: [AppController],
  providers: [AppService, ConfigService]
})
export class AppModule {}
