import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString, Length, Matches, IsOptional } from 'class-validator'

export class PnrRetrieveDto {
  @ApiProperty({
    description: 'PNR locator/control number to retrieve',
    example: 'BRLII9',
    minLength: 6,
    maxLength: 10
  })
  @IsNotEmpty()
  @IsString()
  @Length(6, 10)
  @Matches(/^[A-Z0-9]+$/, {
    message: 'PNR locator must contain only uppercase letters and numbers'
  })
  pnrLocator: string


}