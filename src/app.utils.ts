import * as bcrypt from 'bcrypt'
import { DataSource } from './app.types'
import * as moment from 'moment-timezone'

export function generateRandomCode(length: number) {
  return Array(length)
    .fill(0)
    .map(() => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      return chars.charAt(Math.floor(Math.random() * chars.length))
    })
    .join('')
}

export function generateRandomNumber(length: number) {
  return Array(length)
    .fill(0)
    .map(() => {
      const chars = '0123456789'
      return chars.charAt(Math.floor(Math.random() * chars.length))
    })
    .join('')
}

export async function hashPassword(password: string) {
  const saltRounds = 10
  return await bcrypt.hash(password, saltRounds)
}

export function getDateUTC(userTimeZone: string) {
  const date = new Date()
  // Convert to user's timezone, find start/end of their day, result is in UTC
  const userStartOfDay = moment.tz(date, userTimeZone).startOf('day').toDate()
  const userEndOfDay = moment.tz(date, userTimeZone).endOf('day').toDate()
  // userStartOfDay and userEndOfDay are now JavaScript Date objects in UTC
  // representing the start/end of day in the user's timezone

  return { from: userStartOfDay, to: userEndOfDay }
}

export function getSourceType(source: DataSource) {
  if (source === DataSource.RING) {
    return 'ring'
  }
  return 'phone'
}
